#!/usr/bin/env python3
"""
TVTH January 2025 Issues Backfill Script
========================================

This script specifically targets the Air4 Channels Testing environment issues
from January 2025 to process 5,000 records for the TVTH area.

Target Area Path: Air4 Channels Testing\Year - 2025\Environment Issues_ TVTH - 2025\Jan TVTH issues

Usage:
    python backfill_tvth_jan2025.py --dry-run
    python backfill_tvth_jan2025.py --execute
    python backfill_tvth_jan2025.py --execute --max-records 1000
"""

import asyncio
import sys
import os
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, Any

# Add the functions directory to the path for local execution
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'functions'))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Target configuration for TVTH issues (using realistic date range)
TARGET_AREA_PATH = "Air4 Channels Testing\\Year - 2025\\Environment Issues_ TVTH - 2025\\Jan TVTH issues"
DEFAULT_MAX_RECORDS = 5000
# Use last 90 days instead of future dates for testing
SEARCH_START = datetime.utcnow() - timedelta(days=90)
SEARCH_END = datetime.utcnow()


class TVTHBackfillExecutor:
    """Specialized executor for TVTH January 2025 backfill."""
    
    def __init__(self):
        self.start_time = datetime.utcnow()
    
    async def execute_tvth_backfill(
        self, 
        dry_run: bool = True,
        max_records: int = DEFAULT_MAX_RECORDS,
        batch_size: int = 100
    ) -> Dict[str, Any]:
        """
        Execute TVTH January 2025 backfill.
        
        Args:
            dry_run: If true, shows what would be processed without making changes
            max_records: Maximum number of records to process (default: 5000)
            batch_size: Batch size for processing (default: 100)
            
        Returns:
            Dictionary with execution results
        """
        try:
            print(f"🎯 TVTH JANUARY 2025 BACKFILL")
            print("=" * 80)
            print(f"📂 Target Area: {TARGET_AREA_PATH}")
            print(f"📅 Date Range: {SEARCH_START.strftime('%Y-%m-%d')} to {SEARCH_END.strftime('%Y-%m-%d')}")
            print(f"📊 Max Records: {max_records}")
            print(f"📦 Batch Size: {batch_size}")
            print(f"🧪 Dry Run: {dry_run}")
            print()
            
            # Import the backfill logic
            from __app__.backfill_job import get_clients, fetch_work_items_for_backfill, process_work_item_batch
            from __app__.common.utils.config import get_config
            
            # Load local settings
            self._load_local_settings()
            
            # Initialize clients
            print("🔧 Initializing clients...")
            clients = await get_clients()
            config = get_config()
            
            # Override config with parameters
            config.BACKFILL_BATCH_SIZE = batch_size
            
            print(f"📊 Fetching TVTH work items from last 90 days...")
            print(f"   Area Path: {TARGET_AREA_PATH}")
            print(f"   Date Range: {SEARCH_START} to {SEARCH_END}")

            # Fetch work items with specific area path and date range
            work_items = await fetch_work_items_for_backfill(
                clients['ado'],
                SEARCH_START,
                SEARCH_END,
                area_path=TARGET_AREA_PATH,
                max_records=max_records
            )
            
            print(f"✅ Found {len(work_items)} TVTH work items to process")
            
            if len(work_items) == 0:
                print("⚠️  No work items found matching the criteria")
                print("   This could mean:")
                print("   - No items exist in the specified area path")
                print("   - The area path format is incorrect")
                print("   - The date range doesn't contain any items")
                print()
                print("💡 Suggestions:")
                print("   - Verify the area path exists in Azure DevOps")
                print("   - Check if there are items in parent area paths")
                print("   - Try a broader date range")
                
                return {
                    "success": True,
                    "dry_run": dry_run,
                    "work_items_found": 0,
                    "message": "No work items found matching criteria",
                    "execution_time_seconds": (datetime.utcnow() - self.start_time).total_seconds()
                }
            
            # Show sample of found items
            print("\n📋 Sample of found work items:")
            print("-" * 60)
            for i, item in enumerate(work_items[:10]):  # Show first 10
                print(f"{i+1:2d}. #{item.id} - {item.title[:80]}{'...' if len(item.title) > 80 else ''}")
                print(f"     Type: {item.work_item_type}, State: {item.state}")
                print(f"     Area: {item.area_path}")
                print(f"     Created: {item.created_date}")
                print()
            
            if len(work_items) > 10:
                print(f"     ... and {len(work_items) - 10} more items")
            
            if dry_run:
                estimated_batches = (len(work_items) + batch_size - 1) // batch_size
                print(f"\n📈 Would process in {estimated_batches} batches of {batch_size} items each")
                print(f"⏱️  Estimated processing time: {estimated_batches * 2:.1f} minutes")
                
                return {
                    "success": True,
                    "dry_run": True,
                    "work_items_found": len(work_items),
                    "estimated_batches": estimated_batches,
                    "target_area_path": TARGET_AREA_PATH,
                    "date_range": {
                        "start": SEARCH_START.isoformat(),
                        "end": SEARCH_END.isoformat()
                    },
                    "execution_time_seconds": (datetime.utcnow() - self.start_time).total_seconds()
                }
            
            # Process work items in batches
            print(f"\n🚀 Processing {len(work_items)} TVTH work items in batches of {batch_size}...")
            processed_count = 0
            failed_count = 0
            
            total_batches = (len(work_items) + batch_size - 1) // batch_size
            
            for i in range(0, len(work_items), batch_size):
                batch = work_items[i:i + batch_size]
                batch_num = (i // batch_size) + 1
                
                print(f"📦 Processing batch {batch_num}/{total_batches} ({len(batch)} items)...")
                
                batch_results = await process_work_item_batch(batch, clients)
                processed_count += batch_results['processed']
                failed_count += batch_results['failed']
                
                print(f"   ✅ Processed: {batch_results['processed']}, ❌ Failed: {batch_results['failed']}")
                
                # Progress indicator
                progress = (batch_num / total_batches) * 100
                print(f"   📊 Progress: {progress:.1f}% ({batch_num}/{total_batches} batches)")
            
            execution_time = (datetime.utcnow() - self.start_time).total_seconds()
            
            print(f"\n🎯 TVTH BACKFILL COMPLETED")
            print("=" * 80)
            print(f"📂 Area Path: {TARGET_AREA_PATH}")
            print(f"📊 Total work items: {len(work_items)}")
            print(f"✅ Successfully processed: {processed_count}")
            print(f"❌ Failed: {failed_count}")
            print(f"⏱️  Execution time: {execution_time:.2f} seconds ({execution_time/60:.1f} minutes)")
            
            return {
                "success": True,
                "dry_run": False,
                "total_work_items": len(work_items),
                "processed_count": processed_count,
                "failed_count": failed_count,
                "target_area_path": TARGET_AREA_PATH,
                "date_range": {
                    "start": SEARCH_START.isoformat(),
                    "end": SEARCH_END.isoformat()
                },
                "execution_time_seconds": execution_time
            }
            
        except Exception as e:
            execution_time = (datetime.utcnow() - self.start_time).total_seconds()
            logger.error(f"TVTH backfill execution failed: {e}")
            
            print(f"\n❌ TVTH BACKFILL FAILED")
            print("=" * 80)
            print(f"Error: {str(e)}")
            print(f"⏱️  Execution time: {execution_time:.2f} seconds")
            
            return {
                "success": False,
                "error": str(e),
                "execution_time_seconds": execution_time
            }
    
    def _load_local_settings(self):
        """Load local.settings.json for local execution."""
        try:
            settings_path = os.path.join(os.path.dirname(__file__), '..', 'functions', 'local.settings.json')
            if os.path.exists(settings_path):
                with open(settings_path, 'r') as f:
                    settings = json.load(f)
                    
                # Set environment variables
                for key, value in settings.get('Values', {}).items():
                    os.environ[key] = str(value)
                    
                print(f"✅ Loaded local settings from {settings_path}")
            else:
                print(f"⚠️  No local.settings.json found at {settings_path}")
                
        except Exception as e:
            print(f"⚠️  Failed to load local settings: {e}")


async def main():
    """Main execution function."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Execute TVTH January 2025 backfill")
    
    # Execution mode
    mode_group = parser.add_mutually_exclusive_group(required=True)
    mode_group.add_argument("--dry-run", action="store_true", help="Show what would be processed without making changes")
    mode_group.add_argument("--execute", action="store_true", help="Execute the backfill")
    
    # Parameters
    parser.add_argument("--max-records", type=int, default=DEFAULT_MAX_RECORDS, help=f"Maximum number of records to process (default: {DEFAULT_MAX_RECORDS})")
    parser.add_argument("--batch-size", type=int, default=100, help="Batch size for processing (default: 100)")
    
    args = parser.parse_args()
    
    print("🎯 TVTH JANUARY 2025 BACKFILL SCRIPT")
    print("=" * 80)
    print(f"🕐 Started at: {datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S UTC')}")
    print(f"📂 Target: {TARGET_AREA_PATH}")
    print()
    
    executor = TVTHBackfillExecutor()
    
    try:
        result = await executor.execute_tvth_backfill(
            dry_run=args.dry_run,
            max_records=args.max_records,
            batch_size=args.batch_size
        )
        
        if result['success']:
            if args.dry_run:
                print(f"\n🎉 Dry run completed successfully!")
                print(f"   Found {result['work_items_found']} items ready for processing")
                print(f"   Run with --execute to process them")
            else:
                print(f"\n🎉 TVTH backfill execution completed successfully!")
                print(f"   Processed {result['processed_count']} items")
            return 0
        else:
            print(f"\n💥 TVTH backfill execution failed!")
            return 1
            
    except Exception as e:
        print(f"\n💥 Execution failed: {str(e)}")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
