#!/usr/bin/env python3
"""
Manual Backfill Execution Script
================================

This script executes the backfill job to update the vector database with historical work items.
It can run locally or call the Azure Function HTTP endpoint.

Usage:
    python execute_backfill.py --local --days-back 30 --batch-size 50
    python execute_backfill.py --remote --function-url "https://your-app.azurewebsites.net" --function-key "your-key"
    python execute_backfill.py --dry-run --days-back 7
"""

import asyncio
import sys
import os
import json
import logging
import argparse
import aiohttp
from datetime import datetime, timedelta
from typing import Dict, Any, Optional

# Add the functions directory to the path for local execution
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'functions'))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class BackfillExecutor:
    """Handles backfill execution both locally and remotely."""
    
    def __init__(self):
        self.start_time = datetime.utcnow()
    
    async def execute_local_backfill(
        self,
        days_back: int = 30,
        batch_size: int = 50,
        dry_run: bool = False,
        area_path: str = None,
        max_records: int = None
    ) -> Dict[str, Any]:
        """
        Execute backfill locally using the function code directly.

        Args:
            days_back: Number of days to look back
            batch_size: Batch size for processing
            dry_run: If true, shows what would be processed without making changes
            area_path: Optional area path filter (supports UNDER semantics)
            max_records: Optional maximum number of records to process

        Returns:
            Dictionary with execution results
        """
        try:
            print(f"🔄 EXECUTING LOCAL BACKFILL")
            print("=" * 60)
            print(f"📅 Days back: {days_back}")
            print(f"📦 Batch size: {batch_size}")
            print(f"🧪 Dry run: {dry_run}")
            if area_path:
                print(f"📂 Area path: {area_path}")
            if max_records:
                print(f"📊 Max records: {max_records}")
            print()
            
            # Import the backfill logic
            from __app__.backfill_job import get_clients, fetch_work_items_for_backfill, process_work_item_batch
            from __app__.common.utils.config import get_config
            
            # Load local settings
            self._load_local_settings()
            
            # Initialize clients
            print("🔧 Initializing clients...")
            clients = await get_clients()
            config = get_config()
            
            # Override config with parameters
            config.BACKFILL_DAYS_BACK = days_back
            config.BACKFILL_BATCH_SIZE = batch_size
            
            # Determine date range
            start_date = datetime.utcnow() - timedelta(days=days_back)
            end_date = datetime.utcnow()
            
            print(f"📊 Fetching work items from {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}...")
            
            # Fetch work items
            work_items = await fetch_work_items_for_backfill(
                clients['ado'],
                start_date,
                end_date,
                area_path=area_path,
                max_records=max_records
            )
            
            print(f"✅ Found {len(work_items)} work items to process")
            
            if dry_run:
                print("\n🧪 DRY RUN - Showing what would be processed:")
                print("-" * 50)
                
                for i, item in enumerate(work_items[:10]):  # Show first 10
                    print(f"{i+1:2d}. #{item.id} - {item.title[:60]}{'...' if len(item.title) > 60 else ''}")
                    print(f"     Type: {item.work_item_type}, State: {item.state}")
                
                if len(work_items) > 10:
                    print(f"     ... and {len(work_items) - 10} more items")
                
                estimated_batches = (len(work_items) + batch_size - 1) // batch_size
                print(f"\n📈 Would process in {estimated_batches} batches of {batch_size} items each")
                
                return {
                    "success": True,
                    "dry_run": True,
                    "work_items_found": len(work_items),
                    "estimated_batches": estimated_batches,
                    "execution_time_seconds": (datetime.utcnow() - self.start_time).total_seconds()
                }
            
            # Process work items in batches
            print(f"\n🚀 Processing {len(work_items)} work items in batches of {batch_size}...")
            processed_count = 0
            failed_count = 0
            
            total_batches = (len(work_items) + batch_size - 1) // batch_size
            
            for i in range(0, len(work_items), batch_size):
                batch = work_items[i:i + batch_size]
                batch_num = (i // batch_size) + 1
                
                print(f"📦 Processing batch {batch_num}/{total_batches} ({len(batch)} items)...")
                
                batch_results = await process_work_item_batch(batch, clients)
                processed_count += batch_results['processed']
                failed_count += batch_results['failed']
                
                print(f"   ✅ Processed: {batch_results['processed']}, ❌ Failed: {batch_results['failed']}")
            
            execution_time = (datetime.utcnow() - self.start_time).total_seconds()
            
            print(f"\n🎯 BACKFILL COMPLETED")
            print("=" * 60)
            print(f"📊 Total work items: {len(work_items)}")
            print(f"✅ Successfully processed: {processed_count}")
            print(f"❌ Failed: {failed_count}")
            print(f"⏱️  Execution time: {execution_time:.2f} seconds")
            
            return {
                "success": True,
                "dry_run": False,
                "total_work_items": len(work_items),
                "processed_count": processed_count,
                "failed_count": failed_count,
                "execution_time_seconds": execution_time
            }
            
        except Exception as e:
            execution_time = (datetime.utcnow() - self.start_time).total_seconds()
            logger.error(f"Local backfill execution failed: {e}")
            
            print(f"\n❌ BACKFILL FAILED")
            print("=" * 60)
            print(f"Error: {str(e)}")
            print(f"⏱️  Execution time: {execution_time:.2f} seconds")
            
            return {
                "success": False,
                "error": str(e),
                "execution_time_seconds": execution_time
            }
    
    async def execute_remote_backfill(
        self,
        function_url: str,
        function_key: str,
        days_back: int = 30,
        batch_size: int = 50,
        dry_run: bool = False,
        area_path: str = None,
        max_records: int = None
    ) -> Dict[str, Any]:
        """
        Execute backfill by calling the Azure Function HTTP endpoint.

        Args:
            function_url: Azure Function app URL
            function_key: Function key for authentication
            days_back: Number of days to look back
            batch_size: Batch size for processing
            dry_run: If true, shows what would be processed without making changes
            area_path: Optional area path filter (supports UNDER semantics)
            max_records: Optional maximum number of records to process

        Returns:
            Dictionary with execution results
        """
        try:
            print(f"🌐 EXECUTING REMOTE BACKFILL")
            print("=" * 60)
            print(f"🔗 Function URL: {function_url}")
            print(f"📅 Days back: {days_back}")
            print(f"📦 Batch size: {batch_size}")
            print(f"🧪 Dry run: {dry_run}")
            if area_path:
                print(f"📂 Area path: {area_path}")
            if max_records:
                print(f"📊 Max records: {max_records}")
            print()
            
            # Prepare the request
            url = f"{function_url}/api/backfill_job"
            params = {
                "days_back": days_back,
                "batch_size": batch_size,
                "dry_run": str(dry_run).lower()
            }

            if area_path:
                params["area_path"] = area_path
            if max_records:
                params["max_records"] = max_records
            headers = {
                "x-functions-key": function_key,
                "Content-Type": "application/json"
            }
            
            print(f"📡 Calling Azure Function endpoint...")
            print(f"   URL: {url}")
            print(f"   Parameters: {params}")
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url, params=params, headers=headers) as response:
                    if response.status == 200:
                        result = await response.json()
                        
                        print(f"\n✅ REMOTE BACKFILL COMPLETED")
                        print("=" * 60)
                        
                        if result.get('dry_run'):
                            print(f"🧪 DRY RUN RESULTS:")
                            print(f"📊 Work items found: {result.get('work_items_found', 0)}")
                            print(f"📦 Estimated batches: {result.get('estimated_batches', 0)}")
                            
                            if 'sample_work_items' in result:
                                print(f"\n📋 Sample work items:")
                                for item in result['sample_work_items']:
                                    print(f"   #{item['id']} - {item['title']}")
                        else:
                            print(f"📊 Total work items: {result.get('total_work_items', 0)}")
                            print(f"✅ Successfully processed: {result.get('processed_count', 0)}")
                            print(f"❌ Failed: {result.get('failed_count', 0)}")
                        
                        print(f"⏱️  Execution time: {result.get('execution_time_seconds', 0):.2f} seconds")
                        
                        return result
                    else:
                        error_text = await response.text()
                        raise Exception(f"HTTP {response.status}: {error_text}")
                        
        except Exception as e:
            execution_time = (datetime.utcnow() - self.start_time).total_seconds()
            logger.error(f"Remote backfill execution failed: {e}")
            
            print(f"\n❌ REMOTE BACKFILL FAILED")
            print("=" * 60)
            print(f"Error: {str(e)}")
            print(f"⏱️  Execution time: {execution_time:.2f} seconds")
            
            return {
                "success": False,
                "error": str(e),
                "execution_time_seconds": execution_time
            }
    
    def _load_local_settings(self):
        """Load local.settings.json for local execution."""
        try:
            settings_path = os.path.join(os.path.dirname(__file__), '..', 'functions', 'local.settings.json')
            if os.path.exists(settings_path):
                with open(settings_path, 'r') as f:
                    settings = json.load(f)
                    
                # Set environment variables
                for key, value in settings.get('Values', {}).items():
                    os.environ[key] = str(value)
                    
                print(f"✅ Loaded local settings from {settings_path}")
            else:
                print(f"⚠️  No local.settings.json found at {settings_path}")
                
        except Exception as e:
            print(f"⚠️  Failed to load local settings: {e}")


async def main():
    """Main execution function."""
    parser = argparse.ArgumentParser(description="Execute backfill job to update vector database")
    
    # Execution mode
    mode_group = parser.add_mutually_exclusive_group(required=True)
    mode_group.add_argument("--local", action="store_true", help="Execute locally using function code")
    mode_group.add_argument("--remote", action="store_true", help="Execute remotely via HTTP endpoint")
    
    # Remote execution parameters
    parser.add_argument("--function-url", help="Azure Function app URL (required for remote execution)")
    parser.add_argument("--function-key", help="Function key for authentication (required for remote execution)")
    
    # Backfill parameters
    parser.add_argument("--days-back", type=int, default=30, help="Number of days to look back (default: 30)")
    parser.add_argument("--batch-size", type=int, default=50, help="Batch size for processing (default: 50)")
    parser.add_argument("--dry-run", action="store_true", help="Show what would be processed without making changes")
    parser.add_argument("--area-path", help="Area path filter (supports UNDER semantics)")
    parser.add_argument("--max-records", type=int, help="Maximum number of records to process")
    
    args = parser.parse_args()
    
    # Validate remote execution parameters
    if args.remote and (not args.function_url or not args.function_key):
        parser.error("--function-url and --function-key are required for remote execution")
    
    print("🚀 BACKFILL EXECUTION SCRIPT")
    print("=" * 80)
    print(f"🕐 Started at: {datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S UTC')}")
    print()
    
    executor = BackfillExecutor()
    
    try:
        if args.local:
            result = await executor.execute_local_backfill(
                days_back=args.days_back,
                batch_size=args.batch_size,
                dry_run=args.dry_run,
                area_path=getattr(args, 'area_path', None),
                max_records=getattr(args, 'max_records', None)
            )
        else:  # remote
            result = await executor.execute_remote_backfill(
                function_url=args.function_url,
                function_key=args.function_key,
                days_back=args.days_back,
                batch_size=args.batch_size,
                dry_run=args.dry_run,
                area_path=getattr(args, 'area_path', None),
                max_records=getattr(args, 'max_records', None)
            )
        
        if result['success']:
            print(f"\n🎉 Backfill execution completed successfully!")
            return 0
        else:
            print(f"\n💥 Backfill execution failed!")
            return 1
            
    except Exception as e:
        print(f"\n💥 Execution failed: {str(e)}")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
