"""
Azure Function: Backfill Job
Timer trigger for building historical data and reprocessing work items.
"""

import logging
from datetime import datetime, timedelta
from typing import List, Optional
import azure.functions as func
import json

from ..common.models.schemas import WorkItem
from ..common.adapters.ado_client import Ado<PERSON>lient
from ..common.adapters.search_client import <PERSON><PERSON><PERSON>
from ..common.adapters.vector_storage_factory import VectorStorageFactory
from ..common.ai.embeddings import EmbeddingService
from ..common.utils.config import get_config
from ..common.utils.logging import setup_logging, log_structured

# Initialize logging
setup_logging()
logger = logging.getLogger(__name__)

# Initialize clients (will be lazy-loaded)
_ado_client: Optional[AdoClient] = None
_vector_storage = None
_embedding_service: Optional[EmbeddingService] = None


async def get_clients():
    """Lazy initialization of clients."""
    global _ado_client, _vector_storage, _embedding_service

    if _ado_client is None:
        config = get_config()
        _ado_client = AdoClient(config)
        _vector_storage = VectorStorageFactory.create_vector_storage(config)
        _embedding_service = EmbeddingService(config)

        # Initialize the vector storage (create tables/indexes if needed)
        await VectorStorageFactory.initialize_storage(_vector_storage)

    return {
        'ado': _ado_client,
        'vector_storage': _vector_storage,
        'embedding': _embedding_service
    }


async def main(mytimer: func.TimerRequest) -> None:
    """
    Main function for backfill job.
    Runs on a schedule to process historical work items and update the search index.
    """
    utc_timestamp = datetime.utcnow().replace(tzinfo=None).isoformat()
    
    if mytimer.past_due:
        log_structured(
            logger,
            "warning",
            "Backfill job is running late",
            extra={"timestamp": utc_timestamp}
        )
    
    log_structured(
        logger,
        "info",
        "Starting backfill job",
        extra={"timestamp": utc_timestamp}
    )
    
    try:
        clients = await get_clients()
        config = get_config()
        
        # Determine the date range for backfill
        days_back = config.get('BACKFILL_DAYS_BACK', 30)
        start_date = datetime.utcnow() - timedelta(days=days_back)
        end_date = datetime.utcnow()
        
        log_structured(
            logger,
            "info",
            "Backfill date range determined",
            extra={
                "start_date": start_date.isoformat(),
                "end_date": end_date.isoformat(),
                "days_back": days_back
            }
        )
        
        # Fetch work items from ADO
        work_items = await fetch_work_items_for_backfill(
            clients['ado'], 
            start_date, 
            end_date
        )
        
        log_structured(
            logger,
            "info",
            "Fetched work items for backfill",
            extra={"work_item_count": len(work_items)}
        )
        
        # Process work items in batches
        batch_size = config.get('BACKFILL_BATCH_SIZE', 50)
        processed_count = 0
        failed_count = 0
        
        for i in range(0, len(work_items), batch_size):
            batch = work_items[i:i + batch_size]
            
            log_structured(
                logger,
                "info",
                "Processing backfill batch",
                extra={
                    "batch_start": i,
                    "batch_size": len(batch),
                    "total_items": len(work_items)
                }
            )
            
            batch_results = await process_work_item_batch(batch, clients)
            processed_count += batch_results['processed']
            failed_count += batch_results['failed']
        
        log_structured(
            logger,
            "info",
            "Backfill job completed",
            extra={
                "total_work_items": len(work_items),
                "processed_count": processed_count,
                "failed_count": failed_count,
                "timestamp": utc_timestamp
            }
        )
        
    except Exception as e:
        log_structured(
            logger,
            "error",
            "Backfill job failed",
            extra={
                "error": str(e),
                "timestamp": utc_timestamp
            }
        )
        raise


async def fetch_work_items_for_backfill(
    ado_client: AdoClient,
    start_date: datetime,
    end_date: datetime,
    area_path: str = None,
    max_records: int = None
) -> List[WorkItem]:
    """
    Fetch work items from ADO for the specified date range and optional area path.

    Args:
        ado_client: Azure DevOps client
        start_date: Start date for the query
        end_date: End date for the query
        area_path: Optional area path filter (supports UNDER semantics)
        max_records: Optional maximum number of records to return
    """
    try:
        # Format dates for WIQL (Azure DevOps expects MM/dd/yyyy format)
        start_date_str = start_date.strftime('%m/%d/%Y')
        end_date_str = end_date.strftime('%m/%d/%Y')

        # Build area path filter if specified
        area_filter = ""
        if area_path:
            # Escape single quotes in area path
            escaped_area_path = area_path.replace("'", "''")
            area_filter = f"AND [System.AreaPath] UNDER '{escaped_area_path}'"

        # Build WIQL query for work items in date range
        wiql_query = f"""
        SELECT [System.Id], [System.Title], [System.Description], [System.WorkItemType],
               [System.State], [System.AreaPath], [System.AssignedTo], [System.CreatedDate],
               [System.ChangedDate], [Microsoft.VSTS.Common.Priority], [System.Tags]
        FROM WorkItems
        WHERE [System.CreatedDate] >= '{start_date_str}'
          AND [System.CreatedDate] <= '{end_date_str}'
          AND [System.WorkItemType] IN ('Bug', 'Task', 'User Story', 'Feature')
          AND [System.State] <> 'Removed'
          {area_filter}
        ORDER BY [System.CreatedDate] DESC
        """

        # Log the WIQL query for debugging
        logger.info(f"Executing WIQL query: {wiql_query.strip()}")

        work_items = await ado_client.query_work_items(wiql_query)

        # Apply max_records limit if specified
        if max_records and len(work_items) > max_records:
            work_items = work_items[:max_records]
            logger.info(f"Limited results to {max_records} records as requested")

        # Convert to WorkItem models
        work_item_models = []
        for item in work_items:
            try:
                work_item = WorkItem(
                    id=item.get('id'),
                    title=item.get('fields', {}).get('System.Title', ''),
                    description=item.get('fields', {}).get('System.Description', ''),
                    work_item_type=item.get('fields', {}).get('System.WorkItemType', ''),
                    state=item.get('fields', {}).get('System.State', ''),
                    area_path=item.get('fields', {}).get('System.AreaPath', ''),
                    assigned_to=item.get('fields', {}).get('System.AssignedTo', {}).get('displayName', ''),
                    created_date=item.get('fields', {}).get('System.CreatedDate', ''),
                    changed_date=item.get('fields', {}).get('System.ChangedDate', ''),
                    priority=item.get('fields', {}).get('Microsoft.VSTS.Common.Priority', 2),
                    tags=item.get('fields', {}).get('System.Tags', ''),
                )
                work_item_models.append(work_item)
            except Exception as e:
                logger.warning(f"Failed to parse work item {item.get('id')}: {e}")
                continue
        
        return work_item_models
        
    except Exception as e:
        logger.error(f"Failed to fetch work items for backfill: {e}")
        raise


async def process_work_item_batch(
    work_items: List[WorkItem],
    clients: dict
) -> dict:
    """
    Process a batch of work items for indexing.
    """
    processed = 0
    failed = 0
    
    for work_item in work_items:
        try:
            # Generate embeddings for the work item
            embedding = await clients['embedding'].embed_work_item(work_item)
            
            # Update work item with embedding
            work_item_dict = work_item.dict()
            work_item_dict['embedding'] = embedding
            
            # Upsert to vector storage (SQL or Azure Search)
            await clients['vector_storage'].upsert_work_item_with_embedding(
                work_item,
                embedding
            )
            
            processed += 1
            
            log_structured(
                logger,
                "debug",
                "Processed work item for backfill",
                extra={
                    "work_item_id": work_item.id,
                    "title": work_item.title[:50] + "..." if len(work_item.title) > 50 else work_item.title
                }
            )
            
        except Exception as e:
            failed += 1
            log_structured(
                logger,
                "warning",
                "Failed to process work item for backfill",
                extra={
                    "work_item_id": work_item.id,
                    "error": str(e)
                }
            )
    
    return {
        'processed': processed,
        'failed': failed
    }


# Timer trigger configuration
# This function runs every day at 2 AM UTC
# NCRONTAB expression: "0 0 2 * * *" (seconds, minutes, hours, day, month, day-of-week)
# For testing, you can use "0 */5 * * * *" to run every 5 minutes


async def manual_backfill_execution(req: func.HttpRequest) -> func.HttpResponse:
    """
    Manual HTTP trigger for backfill job execution.

    Query parameters:
    - days_back: Number of days to look back (default: 30)
    - batch_size: Batch size for processing (default: 50)
    - dry_run: If true, shows what would be processed without making changes (default: false)
    - area_path: Optional area path filter (supports UNDER semantics)
    - max_records: Optional maximum number of records to process

    Returns:
        HTTP response with backfill execution results
    """
    start_time = datetime.utcnow()

    try:
        # Parse query parameters
        days_back = int(req.params.get('days_back', 30))
        batch_size = int(req.params.get('batch_size', 50))
        dry_run = req.params.get('dry_run', 'false').lower() == 'true'
        area_path = req.params.get('area_path', None)
        max_records = int(req.params.get('max_records')) if req.params.get('max_records') else None

        log_structured(
            logger,
            "info",
            "Starting manual backfill job execution",
            extra={
                "days_back": days_back,
                "batch_size": batch_size,
                "dry_run": dry_run,
                "area_path": area_path,
                "max_records": max_records,
                "timestamp": start_time.isoformat()
            }
        )

        # Initialize clients
        clients = await get_clients()
        config = get_config()

        # Override config with request parameters
        config.BACKFILL_DAYS_BACK = days_back
        config.BACKFILL_BATCH_SIZE = batch_size

        # Determine the date range for backfill
        start_date = datetime.utcnow() - timedelta(days=days_back)
        end_date = datetime.utcnow()

        log_structured(
            logger,
            "info",
            "Manual backfill date range determined",
            extra={
                "start_date": start_date.isoformat(),
                "end_date": end_date.isoformat(),
                "days_back": days_back
            }
        )

        # Fetch work items from ADO
        work_items = await fetch_work_items_for_backfill(
            clients['ado'],
            start_date,
            end_date,
            area_path=area_path,
            max_records=max_records
        )

        log_structured(
            logger,
            "info",
            "Fetched work items for manual backfill",
            extra={"work_item_count": len(work_items)}
        )

        if dry_run:
            # Return what would be processed without actually processing
            result = {
                "success": True,
                "dry_run": True,
                "work_items_found": len(work_items),
                "date_range": {
                    "start_date": start_date.isoformat(),
                    "end_date": end_date.isoformat(),
                    "days_back": days_back
                },
                "batch_size": batch_size,
                "estimated_batches": (len(work_items) + batch_size - 1) // batch_size,
                "sample_work_items": [
                    {
                        "id": item.id,
                        "title": item.title[:50] + "..." if len(item.title) > 50 else item.title,
                        "work_item_type": item.work_item_type,
                        "state": item.state,
                        "created_date": item.created_date.isoformat() if item.created_date else None
                    } for item in work_items[:5]
                ],
                "execution_time_seconds": (datetime.utcnow() - start_time).total_seconds()
            }

            return func.HttpResponse(
                json.dumps(result, indent=2),
                status_code=200,
                headers={"Content-Type": "application/json"}
            )

        # Process work items in batches
        processed_count = 0
        failed_count = 0

        for i in range(0, len(work_items), batch_size):
            batch = work_items[i:i + batch_size]

            log_structured(
                logger,
                "info",
                "Processing manual backfill batch",
                extra={
                    "batch_start": i,
                    "batch_size": len(batch),
                    "total_items": len(work_items)
                }
            )

            batch_results = await process_work_item_batch(batch, clients)
            processed_count += batch_results['processed']
            failed_count += batch_results['failed']

        execution_time = (datetime.utcnow() - start_time).total_seconds()

        log_structured(
            logger,
            "info",
            "Manual backfill job completed",
            extra={
                "total_work_items": len(work_items),
                "processed_count": processed_count,
                "failed_count": failed_count,
                "execution_time_seconds": execution_time,
                "timestamp": datetime.utcnow().isoformat()
            }
        )

        # Return success response
        result = {
            "success": True,
            "dry_run": False,
            "total_work_items": len(work_items),
            "processed_count": processed_count,
            "failed_count": failed_count,
            "date_range": {
                "start_date": start_date.isoformat(),
                "end_date": end_date.isoformat(),
                "days_back": days_back
            },
            "batch_size": batch_size,
            "execution_time_seconds": execution_time,
            "timestamp": datetime.utcnow().isoformat()
        }

        return func.HttpResponse(
            json.dumps(result, indent=2),
            status_code=200,
            headers={"Content-Type": "application/json"}
        )

    except Exception as e:
        execution_time = (datetime.utcnow() - start_time).total_seconds()

        log_structured(
            logger,
            "error",
            "Manual backfill job failed",
            extra={
                "error": str(e),
                "execution_time_seconds": execution_time,
                "timestamp": datetime.utcnow().isoformat()
            }
        )

        error_result = {
            "success": False,
            "error": str(e),
            "execution_time_seconds": execution_time,
            "timestamp": datetime.utcnow().isoformat()
        }

        return func.HttpResponse(
            json.dumps(error_result, indent=2),
            status_code=500,
            headers={"Content-Type": "application/json"}
        )
