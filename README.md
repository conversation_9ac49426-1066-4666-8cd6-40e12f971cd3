# QA AI Triage System

An intelligent defect triage system that automatically assigns work items, detects duplicates, and sets priorities using AI and machine learning.

## 🚀 Features

- **Automated Assignment**: Uses k-nearest neighbors, code ownership, and heuristics to assign work items to the right team members
- **Duplicate Detection**: Identifies potential duplicate work items using hybrid search (text + vector similarity)
- **Priority Calculation**: Automatically sets priority levels based on content analysis and business rules
- **Email Notifications**: Sends daily email reports with professional formatting via Logic Apps
- **Teams Integration**: Sends notifications to Microsoft Teams channels with adaptive cards
- **Load Balancing**: Considers team member workload when making assignments
- **ML Evaluation**: Comprehensive evaluation framework for measuring and improving model performance

## 🏗️ Architecture

```
Azure DevOps → Service Hook → Azure Functions → AI Processing → Updates & Notifications
                                     ↓
                              Azure AI Search ← Embeddings
```

### Key Components

- **Azure Functions**: Serverless compute for processing work item events
- **Azure AI Search**: Hybrid search with vector embeddings and semantic ranking
- **Azure Key Vault**: Secure storage for configuration and secrets
- **Application Insights**: Monitoring, logging, and analytics
- **Azure DevOps**: Source work item management system

## 📁 Project Structure

```
AutoDefectTriage/
├─ functions/                       # Azure Functions app
│  ├─ __app__/                      # Function app root
│  │  ├─ workitem_event/            # Work item event handlers
│  │  ├─ ageing_scan/               # Aging work item scanner
│  │  ├─ step_by_step_workflow/     # Step-by-step workflow execution
│  │  ├─ backfill_job/              # Timer trigger for backfill
│  │  ├─ notification_functions/    # Notification handling functions
│  │  ├─ workflow_orchestrator.py   # Main workflow orchestrator
│  │  └─ common/                    # Shared components
│  │     ├─ adapters/               # External service adapters (ADO, Teams, Search)
│  │     ├─ ai/                     # AI/ML components (assignment, duplicate detection)
│  │     ├─ cards/                  # Teams adaptive cards
│  │     ├─ notifications/          # Notification engine
│  │     ├─ models/                 # Data models and schemas
│  │     ├─ ownership/              # Code ownership logic
│  │     ├─ services/               # Business logic services
│  │     ├─ vectorstore/            # Vector storage implementations
│  │     └─ utils/                  # Utility functions
│  ├─ requirements.txt              # Python dependencies
│  ├─ host.json                     # Function app configuration
│  ├─ local.settings.json.example   # Local development settings template
│  └─ pyproject.toml                # Python project configuration
├─ infrastructure/                  # Infrastructure as Code
│  ├─ bicep/                        # Azure Bicep templates
│  │  ├─ main-notification-deployment.bicep # Main infrastructure template
│  │  └─ parameters/                # Environment-specific parameters
│  ├─ logic-apps/                   # Logic Apps for notifications
│  │  ├─ teams-logic-app.json       # Teams notification Logic App
│  │  ├─ email-logic-app.json       # Email notification Logic App
│  │  └─ deploy-logic-app-*.ps1     # Deployment scripts
│  ├─ pipelines/                    # Azure DevOps pipelines
│  └─ scripts/                      # Infrastructure deployment scripts
├─ scripts/                         # Production utility scripts
│  ├─ Run-StepByStepWorkflow.ps1    # Main workflow execution
│  ├─ Setup-TeamsNotifications.ps1  # Teams setup script
│  ├─ Start-And-Execute-Function.ps1 # Function startup script
│  ├─ run_complete_workflow.py      # Complete workflow runner
│  ├─ demo_workflow.py              # Demo workflow
│  └─ execute_with_real_data.py     # Real data execution
├─ docs/                            # Documentation
│  ├─ runbook.md                    # Operations runbook
│  ├─ step-by-step-workflow.md      # Workflow documentation
│  ├─ teams-notification-setup.md   # Teams setup guide
│  ├─ teams-timeout-handling.md     # Timeout handling guide
│  ├─ email-notifications.md        # Email notification guide
│  └─ azure-search-comprehensive-fields.md # Search configuration
├─ sql/                             # SQL scripts and functions
│  └─ azure_sql_vector_functions.sql # Vector search functions
├─ README.md                        # This file
├─ PROJECT_STATUS.md                # Current project status
└─ HARDCODED_ASSIGNEES_REMOVAL_SUMMARY.md # Recent cleanup summary
```

## 🛠️ Setup and Installation

### Prerequisites

- Azure subscription with appropriate permissions
- Azure DevOps organization and project
- Python 3.11 or later
- Azure CLI
- Azure Functions Core Tools

### Local Development Setup

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd qa-ai-triage
   ```

2. **Install dependencies**
   ```bash
   cd functions
   pip install -r requirements.txt
   ```

3. **Configure local settings**
   ```bash
   cp local.settings.json.template local.settings.json
   # Edit local.settings.json with your configuration
   ```

4. **Run locally**
   ```bash
   func start
   ```

### Azure Deployment

1. **Deploy infrastructure**
   ```bash
   az deployment group create \
     --resource-group rg-qa-ai-triage \
     --template-file infra/bicep/main.bicep \
     --parameters @infra/bicep/parameters.prod.json
   ```

2. **Deploy function app**
   ```bash
   cd functions
   func azure functionapp publish qa-ai-triage-prod
   ```

3. **Configure Azure DevOps service hook**
   - Go to Project Settings → Service hooks
   - Create new subscription for "Web Hooks"
   - Set URL to your function app endpoint
   - Configure filters for work item events

## ⚙️ Configuration

### Environment Variables

| Variable | Description | Required |
|----------|-------------|----------|
| `ADO_ORGANIZATION` | Azure DevOps organization | Yes |
| `ADO_PROJECT` | Azure DevOps project | Yes |
| `ADO_PAT_TOKEN` | Personal Access Token | Yes |
| `AZURE_SEARCH_SERVICE_NAME` | AI Search service name | Yes |
| `AZURE_SEARCH_ADMIN_KEY` | AI Search admin key | Yes |
| `KEY_VAULT_URL` | Azure Key Vault URL | Yes |
| `TEAMS_WEBHOOK_URL` | Teams webhook URL | No |
| `EMBEDDING_PROVIDER` | Embedding provider (openai/azure_openai/sentence_transformers) | No |
| `FALLBACK_ASSIGNEES` | Comma-separated list of fallback assignee emails | No |
| `COMMON_TEAM_ASSIGNEES` | Comma-separated list of common team member emails | No |
| `SECURITY_TEAM_EMAIL` | Security team email for security alerts | No |
| `AGING_SUMMARY_RECIPIENTS` | Comma-separated list of aging summary email recipients | No |

### Key Vault Secrets

Store sensitive configuration in Azure Key Vault:
- `ado-pat-token`: Azure DevOps Personal Access Token
- `azure-search-admin-key`: Azure AI Search admin key
- `openai-api-key`: OpenAI API key (if using OpenAI embeddings)
- `teams-webhook-url`: Microsoft Teams webhook URL

## 🧪 Testing and Validation

### Run Production Scripts
```bash
# Execute complete workflow
cd scripts
python run_complete_workflow.py

# Run demo workflow
python demo_workflow.py

# Execute with real data
python execute_with_real_data.py
```

### PowerShell Scripts
```powershell
# Setup Teams notifications
.\scripts\Setup-TeamsNotifications.ps1

# Run step-by-step workflow
.\scripts\Run-StepByStepWorkflow.ps1

# Start and execute function
.\scripts\Start-And-Execute-Function.ps1
```

## 📊 Monitoring

### Key Metrics

- **Function Success Rate**: Should be > 95%
- **Assignment Coverage**: Percentage of items auto-assigned
- **Assignment Accuracy**: Measured through feedback
- **Duplicate Detection Precision**: False positive rate
- **Processing Time**: Average time per work item

### Application Insights Queries

**Function Success Rate**
```kusto
requests
| where timestamp > ago(24h)
| where name == "workitem_created"
| summarize SuccessRate = round(100.0 * countif(success == true) / count(), 2)
```

**Assignment Performance**
```kusto
traces
| where timestamp > ago(24h)
| where message contains "Work item assigned"
| summarize Count = count() by tostring(customDimensions.assignee)
```

## 🔧 Troubleshooting

### Common Issues

1. **Function not triggering**: Check service hook configuration and function app status
2. **Assignment not working**: Verify search index is populated and CODEOWNERS is accessible
3. **High error rate**: Check Azure service connectivity and configuration values

See [docs/runbook.md](docs/runbook.md) for detailed troubleshooting guide.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Run the test suite
6. Submit a pull request

### Code Style

- Use Black for code formatting
- Follow PEP 8 guidelines
- Add type hints for all functions
- Write comprehensive docstrings

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙋‍♂️ Support

For questions and support:
- Create an issue in the repository
- Contact the development team
- Check the [runbook](docs/runbook.md) for operational guidance

## 🗺️ Roadmap

- [ ] Enhanced ML models for better accuracy
- [ ] Support for additional work item types
- [ ] Integration with more notification channels
- [ ] Advanced analytics and reporting
- [ ] Multi-language support for text processing
- [ ] Real-time feedback loop for continuous learning
