"""
Configuration management for environment variables and Key Vault secrets.
"""

import os
import logging
from typing import Dict, Any, Optional, Union, List
from dataclasses import dataclass, field
from azure.keyvault.secrets import SecretClient
from azure.identity import DefaultAzureCredential
import json

logger = logging.getLogger(__name__)


@dataclass
class Config:
    """Configuration class that loads from environment variables and Key Vault."""
    
    # Azure DevOps settings
    ADO_ORGANIZATION: str = field(default="")
    ADO_PROJECT: str = field(default="")
    ADO_PAT_TOKEN: str = field(default="")

    # Production Safety Guardrails
    ALLOW_PROJECTS: str = field(default="")  # Comma-separated project allow-list
    SAFETY_ONLY_WORKITEM_ID: str = field(default="")  # Optional single work item ID for testing
    READ_ONLY: bool = field(default=True)  # Never mutate ADO by default
    ALLOW_COMMENTS_IN_READ_ONLY: bool = field(default=False)
    AUTO_ASSIGN_MIN_CONF: float = field(default=0.80)
    DISABLE_BOT_ACTIONS: bool = field(default=False)

    # Security & Tenant Restrictions
    TENANT_ID_ALLOWLIST: str = field(default="")  # Comma-separated tenant GUIDs

    # Vector Store Backend Selection
    VECTOR_BACKEND: str = field(default="azure")  # azure|pg|qdrant

    # Azure Search settings
    AZURE_SEARCH_ENDPOINT: str = field(default="")
    AZURE_SEARCH_KEY: str = field(default="")
    SEARCH_INDEX_NAME: str = field(default="workitems")
    # Legacy compatibility
    AZURE_SEARCH_SERVICE_NAME: str = field(default="")
    AZURE_SEARCH_ADMIN_KEY: str = field(default="")

    # PostgreSQL + pgvector settings
    PG_CONN: str = field(default="")  # ********************************/db

    # Qdrant settings
    QDRANT_URL: str = field(default="http://localhost:6333")
    
    # Azure Key Vault settings
    KEY_VAULT_URL: str = field(default="")
    
    # OpenAI settings
    OPENAI_API_KEY: str = field(default="")
    OPENAI_EMBEDDING_MODEL: str = field(default="text-embedding-3-small")
    
    # Azure OpenAI settings
    AZURE_OPENAI_API_KEY: str = field(default="")
    AZURE_OPENAI_ENDPOINT: str = field(default="")
    AZURE_OPENAI_API_VERSION: str = field(default="2024-02-01")
    AZURE_OPENAI_EMBEDDING_MODEL: str = field(default="text-embedding-3-small")
    
    # Embeddings & AI Models
    EMBEDDING_PROVIDER: str = field(default="aoai")  # aoai|openai|hf|sentence_transformers
    EMBEDDING_MODEL: str = field(default="text-embedding-3-large")
    CROSS_ENCODER_MODEL: str = field(default="cross-encoder/ms-marco-MiniLM-L-6-v2")
    EMBEDDING_MAX_TOKENS: int = field(default=8192)
    
    # Teams Notification Modes (choose one)
    TEAMS_WEBHOOK_URL: str = field(default="")  # Mode A: Simple webhook
    MICROSOFT_APP_ID: str = field(default="")  # Mode B: Bot framework
    MICROSOFT_APP_PASSWORD: str = field(default="")
    TEAMS_TEAM_ID: str = field(default="")
    TEAMS_CHANNEL_ID: str = field(default="")
    TEAMS_LOGIC_APP_URL: str = field(default="")  # Alternative: Logic App
    TEAMS_NOTIFICATIONS_ENABLED: bool = field(default=True)
    # Legacy compatibility
    TEAMS_GRAPH_TOKEN: str = field(default="")

    # Email settings
    Email_LOGIC_APP_URL: str = field(default="")

    # Logic App Integration for Teams Personal Chat
    LOGICAPP_URL: str = field(default="")  # Teams personal chat Logic App endpoint
    BYPASS_LOGICAPP_AND_UPDATE_ADO: bool = field(default=False)  # For testing: skip Logic App, update ADO directly

    # Aging summary email settings (can be string or list)
    AGING_SUMMARY_RECIPIENTS: Union[str, List[str]] = field(default="")

    # Vector storage settings
    VECTOR_STORAGE_TYPE: str = field(default="azure_search")  # "azure_search" or "sql"
    SQL_CONNECTION_STRING: str = field(default="")
    SQL_DATABASE_TYPE: str = field(default="azure_sql")  # "azure_sql" or "postgresql"
    
    # Duplicate detection settings
    DUPLICATE_SIMILARITY_THRESHOLD: float = field(default=0.85)
    DUPLICATE_TEXT_THRESHOLD: float = field(default=0.8)
    DUPLICATE_MAX_CANDIDATES: int = field(default=10)
    DUPLICATE_MAX_RESULTS: int = field(default=5)
    DUPLICATE_INCLUDE_CLOSED: bool = field(default=False)
    
    # Assignment settings
    ASSIGNMENT_KNN_K: int = field(default=10)
    ASSIGNMENT_MIN_CONFIDENCE: float = field(default=0.6)
    ASSIGNMENT_LOAD_WEIGHT: float = field(default=0.3)
    ASSIGNMENT_OWNERSHIP_WEIGHT: float = field(default=0.4)
    ASSIGNMENT_SIMILARITY_WEIGHT: float = field(default=0.3)

    # Assignee configuration - comma-separated list of emails
    FALLBACK_ASSIGNEES: str = field(default="")  # Fallback assignees when no historical data available
    COMMON_TEAM_ASSIGNEES: str = field(default="")  # Common team members for Teams cards
    SECURITY_TEAM_EMAIL: str = field(default="<EMAIL>")  # Security team email for alerts
    
    # Priority settings
    PRIORITY_WEIGHT_SEVERITY: float = field(default=0.25)
    PRIORITY_WEIGHT_CUSTOMER: float = field(default=0.20)
    PRIORITY_WEIGHT_SECURITY: float = field(default=0.20)
    PRIORITY_WEIGHT_PERFORMANCE: float = field(default=0.15)
    PRIORITY_WEIGHT_BLOCKING: float = field(default=0.10)
    PRIORITY_WEIGHT_AREA: float = field(default=0.10)
    
    # Backfill settings
    BACKFILL_DAYS_BACK: int = field(default=30)
    BACKFILL_BATCH_SIZE: int = field(default=50)
    
    # Environment
    ENVIRONMENT: str = field(default="dev")
    
    # Application Insights
    APPINSIGHTS_INSTRUMENTATIONKEY: str = field(default="")
    APPLICATIONINSIGHTS_CONNECTION_STRING: str = field(default="")

    # Notification settings
    NOTIFICATIONS_ENABLED: bool = field(default=True)
    PAGERDUTY_ENABLED: bool = field(default=False)
    PAGERDUTY_API_KEY: str = field(default="")
    PAGERDUTY_DEFAULT_SERVICE_KEY: str = field(default="")
    NOTIFICATION_RATE_LIMIT_PER_USER_HOUR: int = field(default=5)
    NOTIFICATION_RATE_LIMIT_PER_CHANNEL_HOUR: int = field(default=20)
    NOTIFICATION_RATE_LIMIT_PER_PAGERDUTY_HOUR: int = field(default=10)
    NOTIFICATION_DEDUP_WINDOW_MINUTES: int = field(default=60)
    NOTIFICATION_QUIET_HOURS_START: str = field(default="22:00")
    NOTIFICATION_QUIET_HOURS_END: str = field(default="08:00")
    NOTIFICATION_QUIET_HOURS_TIMEZONE: str = field(default="UTC")
    NOTIFICATION_CRITICAL_BYPASS_QUIET_HOURS: bool = field(default=True)
    NOTIFICATION_AGING_CHECK_INTERVAL_HOURS: int = field(default=4)
    NOTIFICATION_AUDIT_RETENTION_DAYS: int = field(default=30)
    NOTIFICATION_MAX_PROCESSING_TIME_MS: int = field(default=5000)
    NOTIFICATION_DEFAULT_PROJECT_CONFIG: str = field(default="")

    # Timeout handling configuration
    NOTIFICATION_TIMEOUT_MINUTES: int = field(default=30)
    DEFAULT_TIMEOUT_ASSIGNEE: str = field(default="<EMAIL>")
    TIMEOUT_FOLLOW_UP_ENABLED: bool = field(default=True)
    TIMEOUT_AUTO_ASSIGN_ENABLED: bool = field(default=True)

    # Internal cache for Key Vault secrets
    _kv_client: Optional[SecretClient] = field(default=None, init=False)
    _secret_cache: Dict[str, str] = field(default_factory=dict, init=False)
    
    def __post_init__(self):
        """Initialize configuration after object creation."""
        self._load_from_environment()
        self._initialize_key_vault()
        self._load_from_key_vault()
    
    def _load_from_environment(self):
        """Load configuration from environment variables."""
        for field_name in self.__dataclass_fields__:
            if field_name.startswith('_'):
                continue
            
            env_value = os.getenv(field_name)
            if env_value is not None:
                field_type = self.__dataclass_fields__[field_name].type
                
                # Convert to appropriate type
                if field_type == bool:
                    setattr(self, field_name, env_value.lower() in ('true', '1', 'yes', 'on'))
                elif field_type == int:
                    try:
                        setattr(self, field_name, int(env_value))
                    except ValueError:
                        logger.warning(f"Invalid integer value for {field_name}: {env_value}")
                elif field_type == float:
                    try:
                        setattr(self, field_name, float(env_value))
                    except ValueError:
                        logger.warning(f"Invalid float value for {field_name}: {env_value}")
                else:
                    setattr(self, field_name, env_value)
    
    def _initialize_key_vault(self):
        """Initialize Key Vault client if URL is provided."""
        if self.KEY_VAULT_URL:
            try:
                credential = DefaultAzureCredential()
                self._kv_client = SecretClient(
                    vault_url=self.KEY_VAULT_URL,
                    credential=credential
                )
                logger.info("Key Vault client initialized")
            except Exception as e:
                logger.warning(f"Failed to initialize Key Vault client: {e}")
                self._kv_client = None
    
    def _load_from_key_vault(self):
        """Load sensitive configuration from Key Vault."""
        if not self._kv_client:
            return
        
        # Map of config fields to Key Vault secret names
        secret_mappings = {
            'ADO_PAT_TOKEN': 'ado-pat-token',
            'AZURE_SEARCH_ADMIN_KEY': 'search-admin-key',
            'OPENAI_API_KEY': 'openai-api-key',
            'AZURE_OPENAI_API_KEY': 'azure-openai-api-key',
            'TEAMS_WEBHOOK_URL': 'teams-webhook-url',
            'TEAMS_GRAPH_TOKEN': 'teams-graph-token'
        }
        
        for config_field, secret_name in secret_mappings.items():
            try:
                # Only load from Key Vault if not already set from environment
                if not getattr(self, config_field):
                    secret_value = self._get_secret(secret_name)
                    if secret_value:
                        setattr(self, config_field, secret_value)
                        logger.debug(f"Loaded {config_field} from Key Vault")
            except Exception as e:
                logger.warning(f"Failed to load secret {secret_name}: {e}")
    
    def _get_secret(self, secret_name: str) -> Optional[str]:
        """Get a secret from Key Vault with caching."""
        if secret_name in self._secret_cache:
            return self._secret_cache[secret_name]
        
        if not self._kv_client:
            return None
        
        try:
            secret = self._kv_client.get_secret(secret_name)
            self._secret_cache[secret_name] = secret.value
            return secret.value
        except Exception as e:
            logger.warning(f"Failed to retrieve secret {secret_name}: {e}")
            return None
    
    def get(self, key: str, default: Any = None) -> Any:
        """Get a configuration value with optional default."""
        return getattr(self, key, default)
    
    def set(self, key: str, value: Any) -> None:
        """Set a configuration value."""
        setattr(self, key, value)
    
    def to_dict(self, include_secrets: bool = False) -> Dict[str, Any]:
        """Convert configuration to dictionary."""
        result = {}
        secret_fields = {
            'ADO_PAT_TOKEN', 'AZURE_SEARCH_ADMIN_KEY', 'OPENAI_API_KEY',
            'AZURE_OPENAI_API_KEY', 'TEAMS_WEBHOOK_URL', 'TEAMS_GRAPH_TOKEN'
        }
        
        for field_name in self.__dataclass_fields__:
            if field_name.startswith('_'):
                continue
            
            value = getattr(self, field_name)
            
            if not include_secrets and field_name in secret_fields:
                result[field_name] = "***" if value else ""
            else:
                result[field_name] = value
        
        return result
    
    def get_allowed_projects(self) -> List[str]:
        """Get list of allowed projects from comma-separated string."""
        if not self.ALLOW_PROJECTS:
            return []
        return [p.strip() for p in self.ALLOW_PROJECTS.split(',') if p.strip()]

    def get_allowed_tenants(self) -> List[str]:
        """Get list of allowed tenant IDs from comma-separated string."""
        if not self.TENANT_ID_ALLOWLIST:
            return []
        return [t.strip() for t in self.TENANT_ID_ALLOWLIST.split(',') if t.strip()]

    def is_project_allowed(self, project_name: str) -> bool:
        """Check if a project is in the allow list."""
        allowed_projects = self.get_allowed_projects()
        if not allowed_projects:  # Empty allow list means all projects allowed
            return True
        return project_name in allowed_projects

    def is_work_item_allowed(self, work_item_id: int) -> bool:
        """Check if a work item ID is allowed based on safety settings."""
        if not self.SAFETY_ONLY_WORKITEM_ID:
            return True  # No restriction
        try:
            allowed_id = int(self.SAFETY_ONLY_WORKITEM_ID)
            return work_item_id == allowed_id
        except (ValueError, TypeError):
            return True  # Invalid setting means no restriction

    def get_teams_mode(self) -> str:
        """Determine Teams notification mode based on configuration."""
        if self.MICROSOFT_APP_ID and self.MICROSOFT_APP_PASSWORD:
            return "bot"
        elif self.TEAMS_LOGIC_APP_URL:
            return "logic_app"
        elif self.TEAMS_WEBHOOK_URL:
            return "webhook"
        else:
            return "disabled"

    def validate(self) -> List[str]:
        """Validate configuration and return list of errors."""
        errors = []
        
        # Required fields
        required_fields = [
            'ADO_ORGANIZATION',
            'ADO_PROJECT',
            'ADO_PAT_TOKEN',
            'AZURE_SEARCH_SERVICE_NAME',
            'AZURE_SEARCH_ADMIN_KEY'
        ]
        
        for field in required_fields:
            if not getattr(self, field):
                errors.append(f"Required field {field} is not set")
        
        # Validate vector backend
        valid_backends = ['azure', 'pg', 'qdrant']
        if self.VECTOR_BACKEND not in valid_backends:
            errors.append(f"Invalid vector backend: {self.VECTOR_BACKEND}")

        # Validate embedding provider
        valid_providers = ['aoai', 'openai', 'hf', 'sentence_transformers', 'azure_openai', 'e5_large']
        if self.EMBEDDING_PROVIDER not in valid_providers:
            errors.append(f"Invalid embedding provider: {self.EMBEDDING_PROVIDER}")

        # Validate Teams mode
        teams_mode = self.get_teams_mode()
        if teams_mode == "disabled" and self.TEAMS_NOTIFICATIONS_ENABLED:
            errors.append("Teams notifications enabled but no valid Teams configuration found")
        
        # Validate provider-specific settings
        if self.EMBEDDING_PROVIDER == 'openai' and not self.OPENAI_API_KEY:
            errors.append("OPENAI_API_KEY required for OpenAI embedding provider")
        
        if self.EMBEDDING_PROVIDER == 'azure_openai':
            if not self.AZURE_OPENAI_API_KEY:
                errors.append("AZURE_OPENAI_API_KEY required for Azure OpenAI provider")
            if not self.AZURE_OPENAI_ENDPOINT:
                errors.append("AZURE_OPENAI_ENDPOINT required for Azure OpenAI provider")
        
        # Validate thresholds
        if not 0.0 <= self.DUPLICATE_SIMILARITY_THRESHOLD <= 1.0:
            errors.append("DUPLICATE_SIMILARITY_THRESHOLD must be between 0.0 and 1.0")
        
        if not 0.0 <= self.ASSIGNMENT_MIN_CONFIDENCE <= 1.0:
            errors.append("ASSIGNMENT_MIN_CONFIDENCE must be between 0.0 and 1.0")

        if not 0.0 <= self.AUTO_ASSIGN_MIN_CONF <= 1.0:
            errors.append("AUTO_ASSIGN_MIN_CONF must be between 0.0 and 1.0")
        
        return errors


# Global configuration instance
_config: Optional[Config] = None


def get_config() -> Config:
    """Get the global configuration instance."""
    global _config
    if _config is None:
        _config = Config()
    return _config


def reload_config() -> Config:
    """Reload configuration from environment and Key Vault."""
    global _config
    _config = Config()
    return _config


def validate_config() -> List[str]:
    """Validate the current configuration."""
    config = get_config()
    return config.validate()
