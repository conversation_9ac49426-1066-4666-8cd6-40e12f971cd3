"""
Azure Functions for Intelligent Notifications & Reminders.
"""

import logging
import json
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
import azure.functions as func

from ..common.models.schemas import WorkItem
from ..common.models import ProjectNotificationConfig
from ..common.adapters.ado_client import AdoClient
from ..common.adapters.teams_client import TeamsClient
from ..common.adapters.pagerduty_client import PagerDutyClient
from ..common.adapters.search_client import SearchClient
from ..common.ai.duplicate import DuplicateDetector
from ..common.ai.assigner import AssignmentEngine
from ..common.ai.priority import PriorityEngine
from ..common.notifications.notification_engine import NotificationEngine
from ..common.utils.config import get_config
from ..common.utils.logging import setup_logging, log_structured

# Initialize logging
setup_logging()
logger = logging.getLogger(__name__)

# Global notification engine (initialized once)
_notification_engine: Optional[NotificationEngine] = None


def get_notification_engine() -> NotificationEngine:
    """Get or create the global notification engine instance."""
    global _notification_engine
    
    if _notification_engine is None:
        try:
            config = get_config()
            
            # Initialize clients
            ado_client = AdoClient(config)
            teams_client = TeamsClient(config)
            pagerduty_client = PagerDutyClient(config) if getattr(config, 'PAGERDUTY_ENABLED', False) else None
            # Initialize Azure Search client if properly configured
            search_service_name = getattr(config, 'AZURE_SEARCH_SERVICE_NAME', '')
            search_client = SearchClient(config) if search_service_name and search_service_name.lower() not in ['disabled', 'none'] else None
            
            # Initialize AI engines (optional)
            duplicate_detector = DuplicateDetector(search_client, config) if search_client else None
            assignment_engine = AssignmentEngine(search_client, config) if search_client else None
            priority_engine = PriorityEngine(config) if getattr(config, 'OPENAI_API_KEY', '') else None
            
            # Create notification engine
            _notification_engine = NotificationEngine(
                config=config,
                ado_client=ado_client,
                teams_client=teams_client,
                pagerduty_client=pagerduty_client,
                search_client=search_client,
                duplicate_detector=duplicate_detector,
                assignment_engine=assignment_engine,
                priority_engine=priority_engine
            )
            
            # Load default project configurations
            _load_default_project_configs(_notification_engine)
            
            log_structured(
                logger,
                "info",
                "Notification engine initialized successfully",
                extra={
                    "pagerduty_enabled": pagerduty_client is not None,
                    "search_enabled": search_client is not None,
                    "ai_engines_enabled": all([duplicate_detector, assignment_engine, priority_engine])
                }
            )
            
        except Exception as e:
            log_structured(
                logger,
                "error",
                f"Failed to initialize notification engine: {e}",
                exc_info=True
            )
            raise
    
    return _notification_engine


def _load_default_project_configs(engine: NotificationEngine) -> None:
    """Load default project configurations."""
    try:
        # This would typically load from a configuration store
        # For now, we'll use a default configuration
        default_config = ProjectNotificationConfig(
            project_name="default",
            enabled=True,
            critical_priorities=[1, 2],
            aging_thresholds={
                "P1": "4h",
                "P2": "24h", 
                "P3": "72h",
                "P4": "168h"
            },
            security_keywords=["security", "vulnerability", "exploit", "breach", "cve"],
            customer_impact_keywords=["customer", "production", "outage", "critical", "live"],
            rate_limits={
                "per_user_per_hour": 5,
                "per_channel_per_hour": 20,
                "per_pagerduty_per_hour": 10
            },
            dedup_window_minutes=60
        )
        
        engine.load_project_config("default", default_config)
        
    except Exception as e:
        log_structured(
            logger,
            "error",
            f"Error loading default project configs: {e}",
            exc_info=True
        )


async def process_critical_notification(req: func.HttpRequest) -> func.HttpResponse:
    """
    HTTP trigger for critical work item notifications.
    
    This function receives work item data and processes it for immediate notifications
    if it meets critical criteria.
    """
    try:
        log_structured(
            logger,
            "info",
            "Processing critical notification request"
        )
        
        # Parse request body
        try:
            request_data = req.get_json()
        except ValueError as e:
            log_structured(
                logger,
                "error",
                f"Invalid JSON in request: {e}"
            )
            return func.HttpResponse(
                json.dumps({"error": "Invalid JSON payload"}),
                status_code=400,
                mimetype="application/json"
            )
        
        if not request_data:
            return func.HttpResponse(
                json.dumps({"error": "Empty payload"}),
                status_code=400,
                mimetype="application/json"
            )
        
        # Extract work item data
        work_item_data = request_data.get("work_item")
        if not work_item_data:
            return func.HttpResponse(
                json.dumps({"error": "Missing work_item data"}),
                status_code=400,
                mimetype="application/json"
            )
        
        # Create work item object
        try:
            work_item = WorkItem(**work_item_data)
        except Exception as e:
            log_structured(
                logger,
                "error",
                f"Invalid work item data: {e}",
                extra={"work_item_data": work_item_data}
            )
            return func.HttpResponse(
                json.dumps({"error": f"Invalid work item data: {e}"}),
                status_code=400,
                mimetype="application/json"
            )
        
        # Get notification engine
        engine = get_notification_engine()
        
        # Process notification
        notification_ids = await engine.process_work_item_notification(
            work_item, 
            event_type=request_data.get("event_type", "created")
        )
        
        # Return response
        response_data = {
            "status": "success",
            "work_item_id": work_item.id,
            "notification_ids": notification_ids,
            "notifications_created": len(notification_ids)
        }
        
        log_structured(
            logger,
            "info",
            f"Critical notification processing completed",
            extra={
                "work_item_id": work_item.id,
                "notifications_created": len(notification_ids)
            }
        )
        
        return func.HttpResponse(
            json.dumps(response_data),
            status_code=200,
            mimetype="application/json"
        )
        
    except Exception as e:
        log_structured(
            logger,
            "error",
            f"Error processing critical notification: {e}",
            exc_info=True
        )
        return func.HttpResponse(
            json.dumps({"error": f"Internal server error: {str(e)}"}),
            status_code=500,
            mimetype="application/json"
        )


async def process_aging_reminders(timer: func.TimerRequest) -> None:
    """
    Timer trigger for aging work item reminders.
    
    This function runs on a schedule to check for aging work items
    and send reminder notifications.
    """
    try:
        utc_timestamp = datetime.utcnow().replace(tzinfo=None).isoformat()
        
        if timer.past_due:
            log_structured(
                logger,
                "warning",
                "Aging reminders function is running late"
            )
        
        log_structured(
            logger,
            "info",
            f"Starting aging reminders processing at {utc_timestamp}"
        )
        
        # Get notification engine
        engine = get_notification_engine()
        
        # Get work items to check for aging
        # This would typically query ADO for open work items
        work_items = await _get_aging_work_items(engine.ado_client)
        
        if not work_items:
            log_structured(
                logger,
                "info",
                "No work items found for aging check"
            )
            return
        
        # Process aging notifications
        notification_ids = await engine.process_aging_notifications(work_items)
        
        log_structured(
            logger,
            "info",
            f"Aging reminders processing completed",
            extra={
                "work_items_checked": len(work_items),
                "notifications_created": len(notification_ids),
                "notification_ids": notification_ids
            }
        )
        
    except Exception as e:
        log_structured(
            logger,
            "error",
            f"Error processing aging reminders: {e}",
            exc_info=True
        )


async def handle_notification_webhook(req: func.HttpRequest) -> func.HttpResponse:
    """
    HTTP trigger for notification status webhooks.
    
    This function handles callbacks from notification services (Teams, PagerDuty, etc.)
    to track delivery status and user actions.
    """
    try:
        log_structured(
            logger,
            "info",
            "Processing notification webhook"
        )
        
        # Parse request body
        try:
            webhook_data = req.get_json()
        except ValueError as e:
            log_structured(
                logger,
                "error",
                f"Invalid JSON in webhook: {e}"
            )
            return func.HttpResponse(
                json.dumps({"error": "Invalid JSON payload"}),
                status_code=400,
                mimetype="application/json"
            )
        
        if not webhook_data:
            return func.HttpResponse(
                json.dumps({"error": "Empty payload"}),
                status_code=400,
                mimetype="application/json"
            )
        
        # Get notification engine
        engine = get_notification_engine()
        
        # Handle different webhook types
        webhook_type = webhook_data.get("type", "unknown")
        
        if webhook_type == "teams_action":
            # Handle Teams adaptive card action
            action_data = webhook_data.get("action_data", {})
            success = await engine.teams_client.handle_card_action(action_data)
            
            # Record user action in audit
            notification_id = action_data.get("notification_id")
            if notification_id:
                engine.audit_service.record_user_action(
                    notification_id,
                    action_data.get("action", "unknown"),
                    action_data,
                    webhook_data.get("user_id")
                )
            
        elif webhook_type == "delivery_status":
            # Handle delivery status update
            notification_id = webhook_data.get("notification_id")
            status = webhook_data.get("status")
            if notification_id and status:
                engine.audit_service.update_notification_status(
                    notification_id,
                    status,
                    webhook_data.get("error_message"),
                    webhook_data.get("response_data")
                )
        
        return func.HttpResponse(
            json.dumps({"status": "success"}),
            status_code=200,
            mimetype="application/json"
        )
        
    except Exception as e:
        log_structured(
            logger,
            "error",
            f"Error processing notification webhook: {e}",
            exc_info=True
        )
        return func.HttpResponse(
            json.dumps({"error": f"Internal server error: {str(e)}"}),
            status_code=500,
            mimetype="application/json"
        )


async def check_new_updated_work_items(timer: func.TimerRequest) -> None:
    """
    Timer trigger to check for new/updated work items every 10 minutes.

    This function runs on a schedule to check for recently created or updated
    Bug, Epic, Feature, and Story work items and process them for notifications.
    """
    try:
        utc_timestamp = datetime.utcnow().replace(tzinfo=None).isoformat()

        if timer.past_due:
            log_structured(
                logger,
                "warning",
                "Work item check function is running late"
            )

        log_structured(
            logger,
            "info",
            f"Starting work item check at {utc_timestamp}"
        )

        # Get notification engine
        engine = get_notification_engine()

        # Get recently created/updated work items (last 24 hours for comprehensive check)
        work_items = await _get_recent_work_items(engine.ado_client, minutes_back=1440)

        if not work_items:
            log_structured(
                logger,
                "info",
                "No recent work items found"
            )
            return

        log_structured(
            logger,
            "info",
            f"Found {len(work_items)} recent work items to process"
        )

        # Process each work item for notifications
        total_notifications = 0
        processed_items = 0

        for work_item_data in work_items:
            try:
                # Convert to WorkItem object
                work_item = _convert_ado_to_work_item(work_item_data)

                # Determine event type based on created vs changed date
                created_date = work_item_data.get("fields", {}).get("System.CreatedDate")
                changed_date = work_item_data.get("fields", {}).get("System.ChangedDate")

                # If created within last 4 hours, treat as "created", otherwise "updated"
                if created_date and changed_date:
                    created_dt = datetime.fromisoformat(created_date.replace('Z', '+00:00'))
                    now = datetime.utcnow().replace(tzinfo=created_dt.tzinfo)

                    if (now - created_dt).total_seconds() < 14400:  # 4 hours
                        event_type = "created"
                    else:
                        event_type = "updated"
                else:
                    event_type = "updated"  # Default to updated if dates are unclear

                # Process notification
                notification_ids = await engine.process_work_item_notification(
                    work_item,
                    event_type=event_type
                )

                total_notifications += len(notification_ids)
                processed_items += 1

                log_structured(
                    logger,
                    "debug",
                    f"Processed work item {work_item.id}",
                    extra={
                        "work_item_id": work_item.id,
                        "event_type": event_type,
                        "notifications_created": len(notification_ids)
                    }
                )

            except Exception as e:
                log_structured(
                    logger,
                    "error",
                    f"Error processing work item {work_item_data.get('id', 'unknown')}: {e}",
                    extra={"work_item_data": work_item_data},
                    exc_info=True
                )
                continue

        log_structured(
            logger,
            "info",
            f"Work item check completed",
            extra={
                "work_items_found": len(work_items),
                "work_items_processed": processed_items,
                "total_notifications_created": total_notifications
            }
        )

    except Exception as e:
        log_structured(
            logger,
            "error",
            f"Error in work item check: {e}",
            exc_info=True
        )


async def _get_recent_work_items(ado_client: AdoClient, minutes_back: int = 1440) -> List[Dict[str, Any]]:
    """Get work items created or updated in the last N minutes (default: 24 hours)."""
    try:
        # Calculate time range
        end_time = datetime.utcnow()
        start_time = end_time - timedelta(minutes=minutes_back)

        # Format dates for WIQL (ADO expects ISO format)
        start_time_str = start_time.isoformat()

        # WIQL query for recently created or updated work items
        wiql_query = f"""
        SELECT [System.Id], [System.Title], [System.Description], [System.WorkItemType],
               [System.State], [System.AreaPath], [System.AssignedTo], [System.CreatedDate],
               [System.ChangedDate], [Microsoft.VSTS.Common.Priority], [System.Tags],
               [Microsoft.VSTS.Common.Severity], [Microsoft.VSTS.TCM.ReproSteps],
               [Microsoft.VSTS.TCM.SystemInfo], [System.CreatedBy], [System.ChangedBy]
        FROM WorkItems
        WHERE [System.TeamProject] = @project
          AND (
            [System.CreatedDate] >= '{start_time_str}'
            OR [System.ChangedDate] >= '{start_time_str}'
          )
          AND [System.WorkItemType] IN ('Bug', 'Epic', 'Feature', 'Story', 'User Story')
          AND [System.State] <> 'Removed'
        ORDER BY [System.ChangedDate] DESC
        """

        log_structured(
            logger,
            "debug",
            f"Querying for work items changed since {start_time_str}"
        )

        work_items = await ado_client.query_work_items(wiql_query)

        log_structured(
            logger,
            "info",
            f"Found {len(work_items)} work items in last {minutes_back} minutes ({minutes_back/60:.1f} hours)"
        )

        return work_items

    except Exception as e:
        log_structured(
            logger,
            "error",
            f"Error getting recent work items: {e}",
            exc_info=True
        )
        return []


def _convert_ado_to_work_item(ado_work_item: Dict[str, Any]) -> WorkItem:
    """Convert ADO work item format to our WorkItem model."""
    try:
        fields = ado_work_item.get("fields", {})

        # Extract basic fields
        work_item_data = {
            "id": ado_work_item.get("id"),
            "title": fields.get("System.Title", ""),
            "description": fields.get("System.Description", ""),
            "work_item_type": fields.get("System.WorkItemType", ""),
            "state": fields.get("System.State", ""),
            "area_path": fields.get("System.AreaPath", ""),
            "assigned_to": fields.get("System.AssignedTo", {}).get("displayName", "") if fields.get("System.AssignedTo") else "",
            "created_by": fields.get("System.CreatedBy", {}).get("displayName", "") if fields.get("System.CreatedBy") else "",
            "created_date": fields.get("System.CreatedDate"),
            "changed_date": fields.get("System.ChangedDate"),
            "priority": fields.get("Microsoft.VSTS.Common.Priority"),
            "severity": fields.get("Microsoft.VSTS.Common.Severity"),
            "tags": fields.get("System.Tags", ""),
            "repro_steps": fields.get("Microsoft.VSTS.TCM.ReproSteps", ""),
            "system_info": fields.get("Microsoft.VSTS.TCM.SystemInfo", "")
        }

        # Create WorkItem object
        return WorkItem(**work_item_data)

    except Exception as e:
        log_structured(
            logger,
            "error",
            f"Error converting ADO work item to WorkItem model: {e}",
            extra={"ado_work_item": ado_work_item},
            exc_info=True
        )
        raise


async def _get_aging_work_items(ado_client: AdoClient) -> List[WorkItem]:
    """Get work items that should be checked for aging."""
    try:
        # Calculate time thresholds for aging
        now = datetime.utcnow()

        # Query for work items that might be aging based on different priority thresholds
        wiql_query = f"""
        SELECT [System.Id], [System.Title], [System.Description], [System.WorkItemType],
               [System.State], [System.AreaPath], [System.AssignedTo], [System.CreatedDate],
               [System.ChangedDate], [Microsoft.VSTS.Common.Priority], [System.Tags],
               [Microsoft.VSTS.Common.Severity], [Microsoft.VSTS.TCM.ReproSteps],
               [Microsoft.VSTS.TCM.SystemInfo], [System.CreatedBy]
        FROM WorkItems
        WHERE [System.TeamProject] = @project
          AND [System.State] IN ('New', 'Active', 'Committed', 'In Progress', 'To Do')
          AND [System.WorkItemType] IN ('Bug', 'Epic', 'Feature', 'Story', 'User Story')
          AND [System.CreatedDate] <= '{(now - timedelta(hours=4)).isoformat()}'
        ORDER BY [Microsoft.VSTS.Common.Priority] ASC, [System.CreatedDate] ASC
        """

        work_items_data = await ado_client.query_work_items(wiql_query)

        # Convert to WorkItem objects
        work_items = []
        for item_data in work_items_data:
            try:
                work_item = _convert_ado_to_work_item(item_data)
                work_items.append(work_item)
            except Exception as e:
                log_structured(
                    logger,
                    "error",
                    f"Error converting aging work item {item_data.get('id', 'unknown')}: {e}",
                    exc_info=True
                )
                continue

        log_structured(
            logger,
            "info",
            f"Found {len(work_items)} work items for aging check"
        )

        return work_items

    except Exception as e:
        log_structured(
            logger,
            "error",
            f"Error getting aging work items: {e}",
            exc_info=True
        )
        return []


async def manual_work_item_check(req: func.HttpRequest) -> func.HttpResponse:
    """
    HTTP trigger for manually checking work items (for testing).

    This function provides the same functionality as the timer trigger but can be
    called manually via HTTP for testing purposes.

    Query parameters:
    - minutes_back: Number of minutes to look back (default: 1440 = 24 hours)
    - dry_run: If true, only shows what would be processed without sending notifications
    """
    try:
        log_structured(
            logger,
            "info",
            "Processing manual work item check request"
        )

        # Get query parameters
        minutes_back = int(req.params.get('minutes_back', 1440))  # Default to 24 hours
        dry_run = req.params.get('dry_run', 'false').lower() == 'true'

        log_structured(
            logger,
            "info",
            f"Manual work item check started",
            extra={
                "minutes_back": minutes_back,
                "dry_run": dry_run
            }
        )

        # Get notification engine
        engine = get_notification_engine()

        # Get recently created/updated work items
        work_items = await _get_recent_work_items(engine.ado_client, minutes_back=minutes_back)

        if not work_items:
            response_data = {
                "status": "success",
                "message": f"No work items found in last {minutes_back} minutes",
                "work_items_found": 0,
                "work_items_processed": 0,
                "notifications_created": 0,
                "dry_run": dry_run
            }

            log_structured(
                logger,
                "info",
                "No work items found for manual check"
            )

            return func.HttpResponse(
                json.dumps(response_data, indent=2),
                status_code=200,
                mimetype="application/json"
            )

        log_structured(
            logger,
            "info",
            f"Found {len(work_items)} work items to process"
        )

        # Process each work item
        processed_items = []
        total_notifications = 0
        processed_count = 0

        for work_item_data in work_items:
            try:
                # Convert to WorkItem object
                work_item = _convert_ado_to_work_item(work_item_data)

                # Determine event type
                fields = work_item_data.get("fields", {})
                created_date = fields.get("System.CreatedDate")
                changed_date = fields.get("System.ChangedDate")

                if created_date and changed_date:
                    created_dt = datetime.fromisoformat(created_date.replace('Z', '+00:00'))
                    now = datetime.now(created_dt.tzinfo)

                    if (now - created_dt).total_seconds() < 14400:  # 4 hours
                        event_type = "created"
                    else:
                        event_type = "updated"
                else:
                    event_type = "updated"

                # Process notification (or simulate if dry run)
                if dry_run:
                    # Simulate processing
                    notification_ids = [f"dry-run-{work_item.id}-{event_type}"]
                    log_structured(
                        logger,
                        "info",
                        f"DRY RUN: Would process work item {work_item.id}",
                        extra={
                            "work_item_id": work_item.id,
                            "event_type": event_type,
                            "title": work_item.title[:50]
                        }
                    )
                else:
                    # Actually process notification
                    notification_ids = await engine.process_work_item_notification(
                        work_item,
                        event_type=event_type
                    )

                total_notifications += len(notification_ids)
                processed_count += 1

                # Add to processed items list
                processed_items.append({
                    "work_item_id": work_item.id,
                    "title": work_item.title[:100],
                    "work_item_type": work_item.work_item_type,
                    "state": work_item.state,
                    "event_type": event_type,
                    "notifications_created": len(notification_ids),
                    "notification_ids": notification_ids if not dry_run else ["dry-run"]
                })

            except Exception as e:
                log_structured(
                    logger,
                    "error",
                    f"Error processing work item {work_item_data.get('id', 'unknown')}: {e}",
                    extra={"work_item_data": work_item_data},
                    exc_info=True
                )

                # Add error to processed items
                processed_items.append({
                    "work_item_id": work_item_data.get('id', 'unknown'),
                    "title": work_item_data.get('fields', {}).get('System.Title', 'Unknown')[:100],
                    "error": str(e),
                    "event_type": "error",
                    "notifications_created": 0
                })
                continue

        # Prepare response
        response_data = {
            "status": "success",
            "message": f"Manual work item check completed",
            "work_items_found": len(work_items),
            "work_items_processed": processed_count,
            "notifications_created": total_notifications,
            "dry_run": dry_run,
            "minutes_back": minutes_back,
            "processed_items": processed_items
        }

        log_structured(
            logger,
            "info",
            f"Manual work item check completed",
            extra={
                "work_items_found": len(work_items),
                "work_items_processed": processed_count,
                "notifications_created": total_notifications,
                "dry_run": dry_run
            }
        )

        return func.HttpResponse(
            json.dumps(response_data, indent=2),
            status_code=200,
            mimetype="application/json"
        )

    except Exception as e:
        log_structured(
            logger,
            "error",
            f"Error in manual work item check: {e}",
            exc_info=True
        )

        error_response = {
            "status": "error",
            "message": f"Error processing manual work item check: {str(e)}",
            "error_type": type(e).__name__
        }

        return func.HttpResponse(
            json.dumps(error_response, indent=2),
            status_code=500,
            mimetype="application/json"
        )
