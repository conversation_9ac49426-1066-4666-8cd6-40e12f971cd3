"""
Assignment Engine
Assigns work items to team members using kNN voting, ownership rules, and load balancing.
"""

import logging
from typing import Dict, List, Optional, Tuple, Any
from collections import defaultdict, Counter
from dataclasses import dataclass
from datetime import datetime, timedelta

from ..models.schemas import WorkItem, SearchResult
from ..adapters.search_client import SearchClient
from ..adapters.ado_client import AdoClient
from ..ai.embeddings import EmbeddingService
from ..ownership.codeowners import CodeOwnersParser
from ..ownership.heuristics import OwnershipHeuristics
from ..utils.config import Config
from ..utils.logging import log_structured
import re

logger = logging.getLogger(__name__)


@dataclass
class AssignmentCandidate:
    """Represents a potential assignee for a work item."""
    assignee: str
    confidence_score: float
    reasoning: List[str]
    vote_count: int
    ownership_match: bool
    load_penalty: float


@dataclass
class TeamMemberLoad:
    """Represents current workload for a team member."""
    assignee: str
    active_items: int
    recent_assignments: int
    avg_resolution_time: float
    expertise_areas: List[str]


class AssignmentEngine:
    """Engine for automatically assigning work items to team members."""
    
    def __init__(self, search_client: SearchClient, config: Config):
        self.search_client = search_client
        self.config = config

        # Check if search client is available (Azure Search enabled)
        self.search_enabled = search_client is not None and not getattr(search_client, '_disabled', False)

        self.embedding_service = EmbeddingService(config) if self.search_enabled else None
        self.codeowners_parser = CodeOwnersParser(config)
        self.ownership_heuristics = OwnershipHeuristics(config)
        self.ado_client = AdoClient(config)
        
        # Configuration parameters
        self.knn_k = getattr(config, 'ASSIGNMENT_KNN_K', 10)
        self.min_confidence = getattr(config, 'ASSIGNMENT_MIN_CONFIDENCE', 0.6)
        self.load_balance_weight = getattr(config, 'ASSIGNMENT_LOAD_WEIGHT', 0.3)
        self.ownership_weight = getattr(config, 'ASSIGNMENT_OWNERSHIP_WEIGHT', 0.4)
        self.similarity_weight = getattr(config, 'ASSIGNMENT_SIMILARITY_WEIGHT', 0.3)
        
        # Cache for team member loads
        self._load_cache: Dict[str, TeamMemberLoad] = {}
        self._cache_timestamp: Optional[datetime] = None
        self._cache_ttl = timedelta(minutes=30)
    
    async def assign_work_item(self, work_item: WorkItem) -> Optional[Dict[str, Any]]:
        """
        Assign a work item to the most appropriate team member.

        ⚠️ PRODUCTION SAFETY: Only processes Bug 748404 during development.

        Args:
            work_item: The work item to assign

        Returns:
            Dictionary with assignment details, or None if no suitable assignee found
        """
        # PRODUCTION SAFETY: Only process Bug 748404
        if work_item.id != 748404:
            log_structured(
                logger,
                "warning",
                f"PRODUCTION SAFETY: Assignment engine ignoring work item {work_item.id} - only Bug 748404 is processed",
                extra={"work_item_id": work_item.id, "allowed_id": 748404}
            )
            return None

        try:
            log_structured(
                logger,
                "info",
                "Starting work item assignment using Azure Search vectored history",
                extra={
                    "work_item_id": work_item.id,
                    "work_item_type": work_item.work_item_type,
                    "area_path": work_item.area_path,
                    "data_source": "Azure Search vectored history only"
                }
            )
            
            # Get assignment candidates using multiple strategies
            candidates = await self._get_assignment_candidates(work_item)
            
            if not candidates:
                logger.warning(f"No assignment candidates found for work item {work_item.id}")
                return None
            
            # Sort candidates by confidence score
            candidates.sort(key=lambda x: x.confidence_score, reverse=True)

            # Get top candidates for suggestions
            top_candidates = candidates[:3]  # Top 3 suggestions

            if not top_candidates or top_candidates[0].confidence_score < self.min_confidence:
                log_structured(
                    logger,
                    "warning",
                    "Assignment confidence too low",
                    extra={
                        "work_item_id": work_item.id,
                        "best_confidence": top_candidates[0].confidence_score if top_candidates else 0.0,
                        "min_confidence": self.min_confidence
                    }
                )
                return None

            best_candidate = top_candidates[0]

            # Build suggestions list from vectored history
            suggestions = []
            for candidate in top_candidates:
                # Format reasoning professionally
                reasoning_text = ""
                if isinstance(candidate.reasoning, list):
                    reasoning_text = "; ".join(candidate.reasoning[:2])  # Top 2 reasons
                else:
                    reasoning_text = str(candidate.reasoning)

                suggestions.append({
                    "assignee": candidate.assignee,
                    "confidence": candidate.confidence_score,
                    "reasoning": reasoning_text
                })

            log_structured(
                logger,
                "info",
                "Work item assignment completed with suggestions from vectored history",
                extra={
                    "work_item_id": work_item.id,
                    "primary_assignee": best_candidate.assignee,
                    "primary_confidence": best_candidate.confidence_score,
                    "suggestions_count": len(suggestions)
                }
            )

            return {
                "assigned_to": best_candidate.assignee,
                "confidence_score": best_candidate.confidence_score,
                "reasoning": suggestions[0]["reasoning"],
                "suggestions": suggestions  # Include all suggestions for Teams card
            }
            
        except Exception as e:
            logger.error(f"Error assigning work item {work_item.id}: {e}")
            return None
    
    async def _get_assignment_candidates(self, work_item: WorkItem) -> List[AssignmentCandidate]:
        """
        Get assignment candidates using multiple strategies.
        """
        candidates = {}
        
        # Strategy 1: kNN voting based on similar work items
        knn_candidates = await self._get_knn_candidates(work_item)
        for candidate in knn_candidates:
            if candidate.assignee not in candidates:
                candidates[candidate.assignee] = candidate
            else:
                # Merge with existing candidate
                existing = candidates[candidate.assignee]
                existing.vote_count += candidate.vote_count
                existing.reasoning.extend(candidate.reasoning)
        
        # Strategy 2: Code ownership rules
        ownership_candidates = await self._get_ownership_candidates(work_item)
        for candidate in ownership_candidates:
            if candidate.assignee not in candidates:
                candidates[candidate.assignee] = candidate
            else:
                existing = candidates[candidate.assignee]
                existing.ownership_match = True
                existing.reasoning.append("Code ownership match")

        # Strategy 3: Iteration-based historical analysis
        log_structured(
            logger,
            "info",
            "Starting iteration-based candidate analysis",
            extra={
                "work_item_id": work_item.id,
                "iteration_path": work_item.iteration_path
            }
        )

        iteration_candidates = await self._get_iteration_candidates(work_item)

        log_structured(
            logger,
            "info",
            f"Iteration analysis returned {len(iteration_candidates)} candidates",
            extra={
                "work_item_id": work_item.id,
                "iteration_candidates_count": len(iteration_candidates),
                "iteration_candidates": [{"assignee": c.assignee, "vote_count": c.vote_count, "confidence": c.confidence_score} for c in iteration_candidates[:3]]
            }
        )

        for candidate in iteration_candidates:
            if candidate.assignee not in candidates:
                candidates[candidate.assignee] = candidate
            else:
                existing = candidates[candidate.assignee]
                existing.vote_count += candidate.vote_count
                existing.reasoning.extend(candidate.reasoning)

        # Strategy 3.5: Enhanced team-based suggestions when historical data is limited
        # Count only non-generic candidates (exclude team names like qa-team, test-team)
        generic_team_names = {'qa-team', 'dev-team', 'ops-team', 'test-team', 'frontend-team', 'backend-team', 'security-team', 'data-team', 'devops-team'}
        real_candidates_count = len([c for c in candidates.keys() if c not in generic_team_names])

        if real_candidates_count <= 2:  # If we have very few real candidates from history
            log_structured(
                logger,
                "info",
                f"Limited real candidates ({real_candidates_count}), adding team-based suggestions",
                extra={
                    "work_item_id": work_item.id,
                    "real_candidates": real_candidates_count,
                    "total_candidates": len(candidates)
                }
            )

            team_candidates = await self._get_team_based_candidates(work_item)
            for candidate in team_candidates:
                if candidate.assignee not in candidates:
                    candidates[candidate.assignee] = candidate

        # Strategy 4: Realistic iteration-based assignees (when no historical assignee data available)
        # Check if we have any candidates with actual assignee information
        # Exclude generic team names and heuristic fallback assignees
        generic_assignees = {
            '', 'qa-team', 'dev-team', 'ops-team', 'test-team', 'frontend-team', 'backend-team', 'security-team', 'data-team', 'devops-team',
            '<EMAIL>', '<EMAIL>', '<EMAIL>',
            '<EMAIL>', '<EMAIL>', '<EMAIL>',
            '<EMAIL>', '<EMAIL>',
            '<EMAIL>', '<EMAIL>', '<EMAIL>',
            '<EMAIL>'
        }

        has_real_assignees = any(
            candidate.assignee and candidate.assignee not in generic_assignees
            for candidate in candidates.values()
        )

        log_structured(
            logger,
            "debug",
            "Checking for real assignees in candidates",
            extra={
                "work_item_id": work_item.id,
                "candidates_count": len(candidates),
                "candidate_assignees": [candidate.assignee for candidate in candidates.values()],
                "has_real_assignees": has_real_assignees
            }
        )

        if not has_real_assignees:
            log_structured(
                logger,
                "info",
                "No real assignee data found in historical candidates, using realistic iteration-based assignees",
                extra={"work_item_id": work_item.id, "historical_candidates": len(candidates)}
            )
            # Clear generic candidates and use realistic ones
            candidates.clear()
            realistic_candidates = await self._get_realistic_iteration_assignees(work_item)
            for candidate in realistic_candidates:
                candidates[candidate.assignee] = candidate

        # Strategy 5: Heuristic rules (fallback)
        heuristic_candidates = await self._get_heuristic_candidates(work_item)
        for candidate in heuristic_candidates:
            if candidate.assignee not in candidates:
                candidates[candidate.assignee] = candidate
            else:
                existing = candidates[candidate.assignee]
                existing.reasoning.extend(candidate.reasoning)
        
        # Apply load balancing
        await self._apply_load_balancing(list(candidates.values()))
        
        # Calculate final confidence scores
        for candidate in candidates.values():
            candidate.confidence_score = self._calculate_confidence_score(candidate)

        final_candidates = list(candidates.values())

        log_structured(
            logger,
            "info",
            f"Final assignment candidates generated",
            extra={
                "work_item_id": work_item.id,
                "total_candidates": len(final_candidates),
                "final_candidates": [
                    {
                        "assignee": c.assignee,
                        "confidence_score": c.confidence_score,
                        "vote_count": c.vote_count,
                        "ownership_match": c.ownership_match,
                        "reasoning": c.reasoning[:2] if isinstance(c.reasoning, list) else [str(c.reasoning)]
                    } for c in sorted(final_candidates, key=lambda x: x.confidence_score, reverse=True)[:5]
                ]
            }
        )

        return final_candidates
    
    async def _get_knn_candidates(self, work_item: WorkItem) -> List[AssignmentCandidate]:
        """
        Get assignment candidates using enhanced k-nearest neighbors voting with advanced algorithms.
        Uses real historical data from indexed work items with multi-vector search and re-ranking.
        """
        try:
            # Return empty list if Azure Search is disabled
            if not self.search_enabled:
                log_structured(
                    logger,
                    "info",
                    "Azure Search disabled - skipping kNN candidate analysis",
                    extra={"work_item_id": work_item.id}
                )
                return []
            # Use enhanced multi-vector search for better results
            similar_items_data = await self._enhanced_multi_vector_search(work_item, k=self.knn_k * 2)

            # Convert to expected format for backward compatibility
            similar_items = []
            for item_data in similar_items_data:
                # Create a simple object with the expected attributes
                class SimilarItem:
                    def __init__(self, data):
                        self.work_item_id = data.get('work_item_id', 0)
                        self.title = data.get('title', '')
                        self.assigned_to = data.get('assigned_to', '')
                        self.score = data.get('rrf_score', data.get('score', 0.0))
                        self.state = data.get('state', '')
                        self.area_path = data.get('area_path', '')
                        self.iteration_path = data.get('iteration_path', '')
                        self.search_strategy = data.get('search_strategy', 'hybrid')

                similar_items.append(SimilarItem(item_data))

            log_structured(
                logger,
                "info",
                "Found similar work items using enhanced multi-vector search",
                extra={
                    "work_item_id": work_item.id,
                    "similar_items_count": len(similar_items),
                    "data_source": "Azure Search enhanced multi-vector",
                    "search_strategies": list(set([item.search_strategy for item in similar_items])),
                    "avg_rrf_score": sum([item.score for item in similar_items]) / len(similar_items) if similar_items else 0
                }
            )
            
            # Extract work item IDs for comment analysis
            work_item_ids = [item.work_item_id for item in similar_items if item.work_item_id != work_item.id]

            # Get assignee mentions from comments
            comment_assignees = await self._extract_assignees_from_comments(work_item_ids)

            # Count votes for each assignee (from both assigned_to field and comments)
            assignee_votes = Counter()
            assignee_scores = defaultdict(list)
            assignee_reasoning = defaultdict(list)

            for item in similar_items:
                if item.work_item_id != work_item.id and hasattr(item, 'assigned_to'):
                    assignee = getattr(item, 'assigned_to', None)
                    if assignee:
                        assignee_votes[assignee] += 1
                        assignee_scores[assignee].append(item.score)
                        assignee_reasoning[assignee].append(f"Similar item #{item.work_item_id} - {item.title[:50]}")

                # Count votes from comment mentions
                if item.work_item_id in comment_assignees:
                    for assignee in comment_assignees[item.work_item_id]:
                        assignee_votes[assignee] += 0.5  # Weight comment mentions slightly less
                        assignee_reasoning[assignee].append(f"Mentioned in comments of #{item.work_item_id}")
            
            # Create candidates from votes
            candidates = []
            for assignee, vote_count in assignee_votes.items():
                if assignee_scores[assignee]:
                    avg_score = sum(assignee_scores[assignee]) / len(assignee_scores[assignee])
                else:
                    avg_score = 0.0

                # Combine reasoning from assignments and comments
                reasoning = [f"kNN voting: {vote_count} similar items"]
                if assignee in assignee_reasoning:
                    reasoning.extend(assignee_reasoning[assignee][:2])  # Top 2 reasons

                candidate = AssignmentCandidate(
                    assignee=assignee,
                    confidence_score=0.0,  # Will be calculated later
                    reasoning=reasoning,
                    vote_count=vote_count,
                    ownership_match=False,
                    load_penalty=0.0
                )
                candidates.append(candidate)
            
            return candidates
            
        except Exception as e:
            logger.error(f"Error in kNN candidate selection: {e}")
            return []
    
    async def _get_ownership_candidates(self, work_item: WorkItem) -> List[AssignmentCandidate]:
        """
        Get assignment candidates based on code ownership rules.
        """
        try:
            # Parse area path to determine code ownership
            owners = await self.codeowners_parser.get_owners_for_area(work_item.area_path)
            
            candidates = []
            for owner in owners:
                candidate = AssignmentCandidate(
                    assignee=owner,
                    confidence_score=0.0,
                    reasoning=["Code ownership match"],
                    vote_count=0,
                    ownership_match=True,
                    load_penalty=0.0
                )
                candidates.append(candidate)
            
            return candidates
            
        except Exception as e:
            logger.error(f"Error in ownership candidate selection: {e}")
            return []
    
    async def _get_iteration_candidates(self, work_item: WorkItem) -> List[AssignmentCandidate]:
        """
        Get assignment candidates based on historical assignments in the same iteration.
        """
        try:
            # Return empty list if Azure Search is disabled
            if not self.search_enabled:
                log_structured(
                    logger,
                    "info",
                    "Azure Search disabled - skipping iteration-based candidate analysis",
                    extra={"work_item_id": work_item.id}
                )
                return []

            if not work_item.iteration_path:
                log_structured(
                    logger,
                    "debug",
                    "No iteration path found for work item, skipping iteration-based assignment",
                    extra={"work_item_id": work_item.id}
                )
                return []

            # Extract iteration name from path with improved logic
            iteration_name = self._extract_iteration_name(work_item.iteration_path)

            log_structured(
                logger,
                "info",
                f"Analyzing historical assignments for iteration: {iteration_name}",
                extra={
                    "work_item_id": work_item.id,
                    "iteration_path": work_item.iteration_path,
                    "iteration_name": iteration_name,
                    "extracted_iteration_name": iteration_name
                }
            )

            # First, let's test if Azure Search is working at all
            try:
                test_search = await self.search_client.hybrid_search(
                    query_text="*",
                    filters=None,
                    top=5
                )
                log_structured(
                    logger,
                    "info",
                    f"Azure Search connectivity test",
                    extra={
                        "total_items_in_index": len(test_search),
                        "sample_items": [
                            {
                                "id": item.work_item_id,
                                "title": getattr(item, 'title', '')[:30],
                                "iteration_path": getattr(item, 'iteration_path', ''),
                                "assigned_to": getattr(item, 'assigned_to', ''),
                                "state": getattr(item, 'state', '')
                            } for item in test_search[:3]
                        ]
                    }
                )
            except Exception as e:
                log_structured(
                    logger,
                    "error",
                    f"Azure Search connectivity test failed: {e}",
                    extra={"work_item_id": work_item.id}
                )

            # Search for work items using comprehensive iteration hierarchy patterns
            hierarchy_patterns = self._extract_iteration_hierarchy_patterns(work_item.iteration_path)

            # Build comprehensive search filter for all hierarchy patterns
            search_conditions = []
            for pattern in hierarchy_patterns:
                escaped_pattern = pattern.replace("'", "''")
                # Use both exact match and contains match for maximum coverage
                search_conditions.append(f"iteration_path eq '{escaped_pattern}'")
                search_conditions.append(f"search.ismatch('{escaped_pattern}', 'iteration_path')")

            # Combine all search conditions with OR
            iteration_search = " or ".join(search_conditions)

            # First try with closed items only (preferred for historical analysis)
            iteration_filter = f"({iteration_search}) and assigned_to ne '' and (state eq 'Closed' or state eq 'Resolved' or state eq 'Done')"

            # Test: Check if there are ANY items with this iteration pattern (without state filter)
            try:
                # Use the main iteration name for testing
                test_pattern = iteration_name.replace("'", "''")
                test_iteration_search = await self.search_client.hybrid_search(
                    query_text="*",
                    filters=f"search.ismatch('{test_pattern}', 'iteration_path')",
                    top=10
                )
                log_structured(
                    logger,
                    "info",
                    f"Test search for iteration pattern '{test_pattern}'",
                    extra={
                        "iteration_pattern": test_pattern,
                        "found_items": len(test_iteration_search),
                        "items_with_assignees": len([item for item in test_iteration_search if getattr(item, 'assigned_to', '')]),
                        "items_closed": len([item for item in test_iteration_search if getattr(item, 'state', '') in ['Closed', 'Resolved', 'Done']]),
                        "sample_items": [
                            {
                                "id": item.work_item_id,
                                "iteration_path": getattr(item, 'iteration_path', ''),
                                "assigned_to": getattr(item, 'assigned_to', ''),
                                "state": getattr(item, 'state', ''),
                                "title": getattr(item, 'title', '')[:30]
                            } for item in test_iteration_search[:5]
                        ]
                    }
                )
            except Exception as e:
                log_structured(
                    logger,
                    "error",
                    f"Test iteration search failed: {e}",
                    extra={"iteration_pattern": test_pattern}
                )

            log_structured(
                logger,
                "debug",
                f"Searching for iteration items with filter: {iteration_filter}",
                extra={
                    "work_item_id": work_item.id,
                    "iteration_filter": iteration_filter,
                    "search_query": f"{work_item.title} {work_item.description or ''}"[:100]
                }
            )

            similar_iteration_items = await self.search_client.hybrid_search(
                query_text=f"{work_item.title} {work_item.description or ''}",
                filters=iteration_filter,
                top=20  # Get more items for better historical analysis
            )

            # If no closed items found, try with all assigned items in the iteration (fallback)
            if not similar_iteration_items:
                log_structured(
                    logger,
                    "info",
                    f"No closed items found in iteration, trying with all assigned items",
                    extra={
                        "work_item_id": work_item.id,
                        "iteration_name": iteration_name
                    }
                )

                fallback_filter = f"({iteration_search}) and assigned_to ne ''"
                similar_iteration_items = await self.search_client.hybrid_search(
                    query_text=f"{work_item.title} {work_item.description or ''}",
                    filters=fallback_filter,
                    top=20
                )

                log_structured(
                    logger,
                    "info",
                    f"Fallback search found {len(similar_iteration_items)} assigned items",
                    extra={
                        "work_item_id": work_item.id,
                        "iteration_name": iteration_name,
                        "fallback_results": len(similar_iteration_items)
                    }
                )

            # Debug: Log detailed information about search results
            log_structured(
                logger,
                "debug",
                f"Raw search results details",
                extra={
                    "work_item_id": work_item.id,
                    "iteration_filter": iteration_filter,
                    "results_count": len(similar_iteration_items),
                    "sample_results": [
                        {
                            "id": item.work_item_id,
                            "title": getattr(item, 'title', '')[:50],
                            "assigned_to": getattr(item, 'assigned_to', ''),
                            "iteration_path": getattr(item, 'iteration_path', ''),
                            "state": getattr(item, 'state', ''),
                            "score": getattr(item, 'score', 0.0),
                            "has_assigned_to": hasattr(item, 'assigned_to'),
                            "has_iteration_path": hasattr(item, 'iteration_path')
                        } for item in similar_iteration_items[:3]
                    ]
                }
            )

            log_structured(
                logger,
                "info",
                f"Found {len(similar_iteration_items)} historical items in iteration",
                extra={
                    "work_item_id": work_item.id,
                    "iteration_name": iteration_name,
                    "historical_items_count": len(similar_iteration_items),
                    "historical_items": [{"id": item.work_item_id, "assignee": getattr(item, 'assigned_to', ''), "title": getattr(item, 'title', '')[:50]} for item in similar_iteration_items[:5]]
                }
            )

            # Extract work item IDs for comment analysis
            iteration_work_item_ids = [item.work_item_id for item in similar_iteration_items]

            # Get assignee mentions from comments across the entire iteration hierarchy
            log_structured(
                logger,
                "info",
                f"Extracting assignees from comments across {len(iteration_work_item_ids)} iteration items",
                extra={
                    "work_item_id": work_item.id,
                    "iteration_items_count": len(iteration_work_item_ids),
                    "iteration_name": iteration_name
                }
            )

            iteration_comment_assignees = await self._extract_assignees_from_comments(iteration_work_item_ids)

            # Count assignments by assignee in this iteration
            assignee_stats = defaultdict(lambda: {
                'count': 0,
                'bug_count': 0,
                'task_count': 0,
                'avg_resolution_days': 0,
                'similar_items': [],
                'comment_mentions': []
            })

            for item in similar_iteration_items:
                if hasattr(item, 'assigned_to') and item.assigned_to:
                    assignee = item.assigned_to
                    assignee_stats[assignee]['count'] += 1

                    # Track work item types
                    if hasattr(item, 'work_item_type'):
                        if item.work_item_type.lower() == 'bug':
                            assignee_stats[assignee]['bug_count'] += 1
                        elif item.work_item_type.lower() in ['task', 'user story']:
                            assignee_stats[assignee]['task_count'] += 1

                    # Store similar items for reasoning
                    assignee_stats[assignee]['similar_items'].append({
                        'id': getattr(item, 'work_item_id', 'unknown'),
                        'title': getattr(item, 'title', 'unknown')[:50],
                        'score': getattr(item, 'score', 0.0)
                    })

                # Also count assignees mentioned in comments
                if hasattr(item, 'work_item_id') and item.work_item_id in iteration_comment_assignees:
                    for comment_assignee in iteration_comment_assignees[item.work_item_id]:
                        assignee_stats[comment_assignee]['comment_mentions'].append(f"#{item.work_item_id}")

            # Create candidates based on iteration history
            candidates = []
            total_items = sum(stats['count'] for stats in assignee_stats.values())

            for assignee, stats in assignee_stats.items():
                # Calculate confidence based on historical involvement
                involvement_ratio = stats['count'] / total_items if total_items > 0 else 0

                # Prefer assignees who have worked on similar work item types
                type_bonus = 0.0
                if work_item.work_item_type.lower() == 'bug' and stats['bug_count'] > 0:
                    type_bonus = 0.3
                elif work_item.work_item_type.lower() in ['task', 'user story'] and stats['task_count'] > 0:
                    type_bonus = 0.2

                # Build reasoning
                reasoning = [
                    f"Iteration history: {stats['count']} items in {iteration_name}"
                ]

                if stats['bug_count'] > 0:
                    reasoning.append(f"Bug experience: {stats['bug_count']} bugs resolved")

                if stats['task_count'] > 0:
                    reasoning.append(f"Task experience: {stats['task_count']} tasks completed")

                if stats['comment_mentions']:
                    reasoning.append(f"Mentioned in comments: {', '.join(stats['comment_mentions'][:3])}")

                # Add similar items to reasoning
                if stats['similar_items']:
                    top_similar = sorted(stats['similar_items'], key=lambda x: x['score'], reverse=True)[:2]
                    for item in top_similar:
                        reasoning.append(f"Similar item: #{item['id']} - {item['title']}")

                candidate = AssignmentCandidate(
                    assignee=assignee,
                    confidence_score=involvement_ratio + type_bonus,
                    reasoning=reasoning,
                    vote_count=stats['count'],
                    ownership_match=False,
                    load_penalty=0.0
                )
                candidates.append(candidate)

            # Sort by vote count and confidence
            candidates.sort(key=lambda x: (x.vote_count, x.confidence_score), reverse=True)

            log_structured(
                logger,
                "info",
                f"Found {len(candidates)} iteration-based candidates",
                extra={
                    "work_item_id": work_item.id,
                    "iteration_name": iteration_name,
                    "total_historical_items": total_items,
                    "candidates": [{"assignee": c.assignee, "vote_count": c.vote_count, "confidence": c.confidence_score} for c in candidates[:3]]
                }
            )

            return candidates

        except Exception as e:
            log_structured(
                logger,
                "error",
                f"Error in iteration-based candidate selection: {e}",
                extra={"work_item_id": work_item.id, "iteration_path": work_item.iteration_path}
            )
            return []

    async def _get_realistic_iteration_assignees(self, work_item: WorkItem) -> List[AssignmentCandidate]:
        """
        Get realistic assignee suggestions based on iteration name and work item patterns.
        This provides realistic suggestions when historical data is not available.
        """
        candidates = []

        if not work_item.iteration_path:
            return candidates

        # Extract iteration name using improved logic
        iteration_name = self._extract_iteration_name(work_item.iteration_path)

        # Only get assignees from real historical data - no fallbacks
        if self.search_enabled:
            # Get assignees only from Azure Search vectored historical data
            real_assignees = await self._get_historical_assignees_for_iteration(iteration_name, work_item.iteration_path)
        else:
            # Azure Search is disabled - return empty list (no fallback assignees)
            log_structured(
                logger,
                "warning",
                "Azure Search disabled - cannot provide assignee suggestions without historical data",
                extra={"iteration_name": iteration_name}
            )
            real_assignees = []

        # Use the same assignees for all iteration patterns when using fallback
        iteration_assignees = {}
        if real_assignees:
            iteration_assignees = {
                "Environment Issues_ TVTH - 2025": real_assignees,
                "Sprint 1": real_assignees,
                "Sprint 2": real_assignees,
                "Bug Fix Sprint": real_assignees
            }

        # Get assignees for this iteration
        assignees = iteration_assignees.get(iteration_name, [])

        # If no specific iteration match, use the real assignees
        if not assignees:
            assignees = real_assignees

        # Create candidates
        for i, (assignee, role, confidence) in enumerate(assignees):
            candidate = AssignmentCandidate(
                assignee=assignee,
                vote_count=len(assignees) - i,  # Higher vote count for higher confidence
                confidence_score=confidence,
                reasoning=[f"Iteration specialist: {role} for {iteration_name}"],
                ownership_match=False,
                load_penalty=0.0
            )
            candidates.append(candidate)

        log_structured(
            logger,
            "info",
            f"Generated {len(candidates)} realistic iteration-based assignees",
            extra={
                "work_item_id": work_item.id,
                "iteration_name": iteration_name,
                "candidates": [{"assignee": c.assignee, "confidence": c.confidence_score} for c in candidates]
            }
        )

        return candidates

    async def _get_historical_assignees_for_iteration(self, iteration_name: str, full_iteration_path: str = "") -> List[Tuple[str, str, float]]:
        """
        Get historical assignees for a specific iteration from Azure DevOps data.
        Searches across the full iteration hierarchy including sub-iterations.

        Args:
            iteration_name: The iteration name to search for
            full_iteration_path: The full iteration path to search for (optional)

        Returns:
            List of tuples (assignee_email, role, confidence_score)
        """
        try:
            # Return empty list if Azure Search is disabled
            if not self.search_enabled:
                log_structured(
                    logger,
                    "info",
                    "Azure Search disabled - cannot get historical assignees",
                    extra={"iteration_name": iteration_name, "full_iteration_path": full_iteration_path}
                )
                return []

            log_structured(
                logger,
                "info",
                f"Getting historical assignees for iteration hierarchy",
                extra={
                    "iteration_name": iteration_name,
                    "full_iteration_path": full_iteration_path
                }
            )

            # Build comprehensive search query using hierarchy patterns
            hierarchy_patterns = self._extract_iteration_hierarchy_patterns(full_iteration_path or iteration_name)

            search_conditions = []

            # Add search conditions for all hierarchy patterns
            for pattern in hierarchy_patterns:
                escaped_pattern = pattern.replace("'", "''")
                # Use both exact match and contains match for maximum coverage
                search_conditions.append(f"iteration_path eq '{escaped_pattern}'")
                search_conditions.append(f"search.ismatch('{escaped_pattern}', 'iteration_path')")

            # Also add the extracted iteration name if not already included
            escaped_iteration_name = iteration_name.replace("'", "''")
            if escaped_iteration_name not in [p.replace("'", "''") for p in hierarchy_patterns]:
                search_conditions.append(f"search.ismatch('{escaped_iteration_name}', 'iteration_path')")

            # Combine all search conditions with OR
            iteration_search = " or ".join(search_conditions)

            # First try with closed items only (preferred for historical analysis)
            query = f"({iteration_search}) and assigned_to ne '' and (state eq 'Closed' or state eq 'Resolved' or state eq 'Done')"

            log_structured(
                logger,
                "debug",
                f"Historical assignee search query: {query}",
                extra={
                    "iteration_name": iteration_name,
                    "full_iteration_path": full_iteration_path,
                    "search_conditions": search_conditions,
                    "search_filter": query
                }
            )

            historical_items = await self.search_client.hybrid_search(
                query_text="*",
                filters=query,
                top=50
            )

            # If no closed items found, try with all assigned items in the iteration (fallback)
            if not historical_items:
                log_structured(
                    logger,
                    "info",
                    f"No closed historical items found, trying with all assigned items",
                    extra={
                        "iteration_name": iteration_name,
                        "full_iteration_path": full_iteration_path
                    }
                )

                fallback_query = f"({iteration_search}) and assigned_to ne ''"
                historical_items = await self.search_client.hybrid_search(
                    query_text="*",
                    filters=fallback_query,
                    top=50
                )

                log_structured(
                    logger,
                    "info",
                    f"Fallback historical search found {len(historical_items)} assigned items",
                    extra={
                        "iteration_name": iteration_name,
                        "fallback_results": len(historical_items)
                    }
                )

            # Debug: Log detailed search results for historical assignee analysis
            log_structured(
                logger,
                "debug",
                f"Historical assignee search results details",
                extra={
                    "iteration_name": iteration_name,
                    "search_filter": query,
                    "results_count": len(historical_items),
                    "detailed_results": [
                        {
                            "id": item.work_item_id,
                            "assigned_to": getattr(item, 'assigned_to', ''),
                            "iteration_path": getattr(item, 'iteration_path', ''),
                            "state": getattr(item, 'state', ''),
                            "title": getattr(item, 'title', '')[:30],
                            "has_assignee": bool(getattr(item, 'assigned_to', '')),
                            "assignee_length": len(getattr(item, 'assigned_to', ''))
                        } for item in historical_items[:5]
                    ]
                }
            )

            log_structured(
                logger,
                "info",
                f"Found {len(historical_items)} historical items for assignee analysis",
                extra={
                    "iteration_name": iteration_name,
                    "historical_items_count": len(historical_items),
                    "sample_items": [{"id": item.work_item_id, "assignee": getattr(item, 'assigned_to', ''), "iteration": getattr(item, 'iteration_path', '')} for item in historical_items[:3]]
                }
            )

            # Count assignees from historical data
            assignee_counts = {}
            for item in historical_items:
                assignee = getattr(item, 'assigned_to', '').strip()
                if assignee and assignee.lower() not in ['unassigned', '', 'none']:
                    # Clean up assignee format (remove display names, keep email)
                    if '<' in assignee and '>' in assignee:
                        # <AUTHOR> <EMAIL>" format
                        email_match = assignee.split('<')[1].split('>')[0].strip()
                        assignee = email_match

                    assignee_counts[assignee] = assignee_counts.get(assignee, 0) + 1

            # Convert to the expected format with confidence scores based on frequency
            total_items = len(historical_items)
            historical_assignees = []

            for assignee, count in sorted(assignee_counts.items(), key=lambda x: x[1], reverse=True):
                confidence = min(0.9, 0.5 + (count / total_items) * 0.4)  # Scale confidence 0.5-0.9
                historical_assignees.append((assignee, "Developer", confidence))

            log_structured(
                logger,
                "info",
                f"Historical assignee analysis complete",
                extra={
                    "iteration_name": iteration_name,
                    "total_historical_items": total_items,
                    "unique_assignees": len(assignee_counts),
                    "assignee_counts": dict(list(assignee_counts.items())[:5]),  # Top 5 assignees with counts
                    "top_assignees": [{"assignee": assignee, "role": role, "confidence": confidence} for assignee, role, confidence in historical_assignees[:5]]
                }
            )

            return historical_assignees[:5]  # Return top 5 historical assignees

        except Exception as e:
            logger.warning(f"Error getting historical assignees for iteration {iteration_name}: {e}")
            return []

    async def _get_team_based_candidates(self, work_item: WorkItem) -> List[AssignmentCandidate]:
        """
        Get team-based assignment candidates when historical data is limited.
        This provides more diverse suggestions based on work item type and area.
        """
        try:
            candidates = []

            # Define team members based on work item type and area
            team_suggestions = []

            # Area-based team assignments
            area_lower = work_item.area_path.lower()
            if 'air4' in area_lower or 'channels' in area_lower:
                if work_item.work_item_type.lower() == 'bug':
                    team_suggestions.extend([
                        ("<EMAIL>", "QA Engineer", 0.8),
                        ("<EMAIL>", "Test Automation Engineer", 0.7),
                        ("<EMAIL>", "Development Lead", 0.6),
                        ("<EMAIL>", "System Analyst", 0.5)
                    ])
                elif work_item.work_item_type.lower() in ['task', 'user story']:
                    team_suggestions.extend([
                        ("<EMAIL>", "Development Lead", 0.8),
                        ("<EMAIL>", "Senior Developer", 0.7),
                        ("<EMAIL>", "System Analyst", 0.6),
                        ("<EMAIL>", "Product Owner", 0.5)
                    ])
                else:  # Other work item types
                    team_suggestions.extend([
                        ("<EMAIL>", "Team Lead", 0.7),
                        ("<EMAIL>", "Senior Developer", 0.6),
                        ("<EMAIL>", "System Analyst", 0.5)
                    ])

            # Priority-based adjustments
            if work_item.priority and work_item.priority <= 2:  # High priority
                # Add senior team members for high priority items
                team_suggestions.extend([
                    ("<EMAIL>", "Senior Lead", 0.9),
                    ("<EMAIL>", "Technical Lead", 0.8)
                ])

            # Create candidates from team suggestions
            for assignee, role, confidence in team_suggestions[:5]:  # Limit to top 5
                candidate = AssignmentCandidate(
                    assignee=assignee,
                    confidence_score=confidence,
                    reasoning=[f"Team assignment: {role} for {work_item.work_item_type}"],
                    vote_count=1,
                    ownership_match=False,
                    load_penalty=0.0
                )
                candidates.append(candidate)

            log_structured(
                logger,
                "info",
                f"Generated {len(candidates)} team-based candidates",
                extra={
                    "work_item_id": work_item.id,
                    "work_item_type": work_item.work_item_type,
                    "area_path": work_item.area_path,
                    "team_candidates": [{"assignee": c.assignee, "confidence": c.confidence_score} for c in candidates]
                }
            )

            return candidates

        except Exception as e:
            logger.error(f"Error in team-based candidate selection: {e}")
            return []

    async def _get_heuristic_candidates(self, work_item: WorkItem) -> List[AssignmentCandidate]:
        """
        Get assignment candidates using heuristic rules.
        """
        try:
            # Use heuristics to suggest assignees
            suggestions = await self.ownership_heuristics.suggest_assignees(work_item)

            candidates = []
            for suggestion in suggestions:
                candidate = AssignmentCandidate(
                    assignee=suggestion['assignee'],
                    confidence_score=0.0,
                    reasoning=[suggestion['reason']],
                    vote_count=0,
                    ownership_match=False,
                    load_penalty=0.0
                )
                candidates.append(candidate)

            return candidates

        except Exception as e:
            logger.error(f"Error in heuristic candidate selection: {e}")
            return []
    
    async def _apply_load_balancing(self, candidates: List[AssignmentCandidate]) -> None:
        """
        Apply load balancing penalties to candidates.
        """
        try:
            # Get current team member loads
            loads = await self._get_team_member_loads([c.assignee for c in candidates])
            
            # Calculate load penalties
            if loads:
                max_load = max(load.active_items for load in loads.values())
                
                for candidate in candidates:
                    if candidate.assignee in loads:
                        load = loads[candidate.assignee]
                        # Penalty based on relative load
                        if max_load > 0:
                            candidate.load_penalty = load.active_items / max_load
                        else:
                            candidate.load_penalty = 0.0
            
        except Exception as e:
            logger.error(f"Error applying load balancing: {e}")
    
    async def _get_team_member_loads(self, assignees: List[str]) -> Dict[str, TeamMemberLoad]:
        """
        Get current workload information for team members.
        """
        try:
            # Check cache first
            if (self._cache_timestamp and 
                datetime.now() - self._cache_timestamp < self._cache_ttl):
                return {k: v for k, v in self._load_cache.items() if k in assignees}
            
            # Refresh cache
            loads = {}
            
            for assignee in assignees:
                # Query for active work items assigned to this person
                active_query = f"assigned_to eq '{assignee}' and state ne 'Closed' and state ne 'Resolved'"
                active_items = await self.search_client.hybrid_search(
                    query_text="*",
                    filters=active_query,
                    top=100
                )
                
                # Query for recently completed items (last 30 days)
                recent_query = f"assigned_to eq '{assignee}' and state eq 'Closed'"
                recent_items = await self.search_client.hybrid_search(
                    query_text="*",
                    filters=recent_query,
                    top=50
                )
                
                load = TeamMemberLoad(
                    assignee=assignee,
                    active_items=len(active_items),
                    recent_assignments=len(recent_items),
                    avg_resolution_time=0.0,  # Could be calculated from recent items
                    expertise_areas=[]  # Could be inferred from work history
                )
                
                loads[assignee] = load
            
            # Update cache
            self._load_cache.update(loads)
            self._cache_timestamp = datetime.now()
            
            return loads
            
        except Exception as e:
            logger.error(f"Error getting team member loads: {e}")
            return {}

    async def _extract_assignees_from_comments(self, work_item_ids: List[int]) -> Dict[int, List[str]]:
        """
        Extract assignee information from work item comments with enhanced patterns.

        Args:
            work_item_ids: List of work item IDs to check comments for

        Returns:
            Dictionary mapping work item ID to list of assignees mentioned in comments
        """
        assignee_mentions = {}

        if not work_item_ids:
            return assignee_mentions

        log_structured(
            logger,
            "info",
            f"Extracting assignees from comments for {len(work_item_ids)} work items",
            extra={"work_item_count": len(work_item_ids)}
        )

        # Enhanced email pattern to match assignee mentions
        email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'

        # Enhanced assignee mention patterns
        assignee_patterns = [
            r'assign(?:ed)?\s+to\s+([A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,})',
            r'reassign(?:ed)?\s+to\s+([A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,})',
            r'@([A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,})',
            r'assigned:\s*([A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,})',
            r'owner:\s*([A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,})',
            r'responsible:\s*([A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,})',
            r'handover\s+to\s+([A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,})',
            r'transfer(?:red)?\s+to\s+([A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,})',
            r'please\s+assign\s+to\s+([A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,})',
            r'cc:\s*([A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,})',
            r'contact:\s*([A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,})'
        ]

        total_assignees_found = 0

        for work_item_id in work_item_ids:
            try:
                comments = await self.ado_client.get_comments(work_item_id)
                assignees = set()

                for comment in comments:
                    comment_text = comment.get('text', '').lower()

                    # Extract emails using enhanced patterns
                    for pattern in assignee_patterns:
                        matches = re.findall(pattern, comment_text, re.IGNORECASE)
                        assignees.update(matches)

                    # Also extract all emails mentioned in comments
                    email_matches = re.findall(email_pattern, comment_text, re.IGNORECASE)
                    assignees.update(email_matches)

                if assignees:
                    assignee_mentions[work_item_id] = list(assignees)
                    total_assignees_found += len(assignees)

            except Exception as e:
                logger.warning(f"Error extracting assignees from comments for work item {work_item_id}: {e}")
                continue

        log_structured(
            logger,
            "info",
            f"Comment extraction completed",
            extra={
                "work_items_processed": len(work_item_ids),
                "work_items_with_assignees": len(assignee_mentions),
                "total_assignees_found": total_assignees_found,
                "sample_assignees": list(assignee_mentions.values())[:3] if assignee_mentions else []
            }
        )

        return assignee_mentions

    async def _enhanced_multi_vector_search(self, work_item: WorkItem, k: int = 10) -> List[Dict[str, Any]]:
        """
        Enhanced multi-vector search using title, content, and temporal relevance.
        Implements advanced algorithms for better assignee suggestions.
        """
        try:
            # Prepare multiple search queries with different strategies
            search_queries = []

            # 1. Title-based semantic search
            title_query = f"{work_item.title}"
            if work_item.work_item_type:
                title_query += f" {work_item.work_item_type}"
            search_queries.append({
                "query": title_query,
                "weight": 0.4,
                "strategy": "title_semantic"
            })

            # 2. Content-based search (description + repro steps)
            content_parts = []
            if work_item.description:
                content_parts.append(work_item.description[:500])  # Limit length
            if hasattr(work_item, 'repro_steps') and work_item.repro_steps:
                content_parts.append(work_item.repro_steps[:300])

            if content_parts:
                content_query = " ".join(content_parts)
                search_queries.append({
                    "query": content_query,
                    "weight": 0.3,
                    "strategy": "content_semantic"
                })

            # 3. Technical keyword search
            tech_keywords = self._extract_technical_keywords(work_item)
            if tech_keywords:
                tech_query = " ".join(tech_keywords)
                search_queries.append({
                    "query": tech_query,
                    "weight": 0.2,
                    "strategy": "technical_keywords"
                })

            # 4. Area/iteration context search
            context_parts = []
            if work_item.area_path:
                context_parts.append(work_item.area_path.split('\\')[-1])  # Last part of area
            if work_item.iteration_path:
                context_parts.append(work_item.iteration_path.split('\\')[-1])  # Last part of iteration

            if context_parts:
                context_query = " ".join(context_parts)
                search_queries.append({
                    "query": context_query,
                    "weight": 0.1,
                    "strategy": "context_based"
                })

            # Execute searches and combine results
            all_results = []
            for search_query in search_queries:
                try:
                    # Add temporal relevance filter (prefer items from last 6 months)
                    from datetime import datetime, timedelta
                    six_months_ago = datetime.now() - timedelta(days=180)

                    # Build enhanced filter
                    filters = []
                    filters.append("assigned_to ne null")  # Must have assignee
                    filters.append("assigned_to ne ''")    # Must not be empty
                    filters.append(f"created_date ge {six_months_ago.isoformat()}Z")  # Recent items

                    # Add area/iteration filters for better context
                    if work_item.area_path:
                        area_filter = f"search.ismatch('{work_item.area_path.split('\\')[-1]}', 'area_path')"
                        filters.append(area_filter)

                    filter_expression = " and ".join(filters)

                    results = await self.search_client.hybrid_search(
                        query_text=search_query["query"],
                        top=k,
                        filters=filter_expression
                    )

                    # Add strategy metadata to results
                    for result in results:
                        result_dict = dict(result) if hasattr(result, '__iter__') else {
                            'id': getattr(result, 'id', ''),
                            'work_item_id': getattr(result, 'work_item_id', 0),
                            'title': getattr(result, 'title', ''),
                            'assigned_to': getattr(result, 'assigned_to', ''),
                            'state': getattr(result, 'state', ''),
                            'area_path': getattr(result, 'area_path', ''),
                            'iteration_path': getattr(result, 'iteration_path', ''),
                            'created_date': getattr(result, 'created_date', ''),
                            'score': getattr(result, 'score', 0.0)
                        }
                        result_dict['search_strategy'] = search_query["strategy"]
                        result_dict['strategy_weight'] = search_query["weight"]
                        all_results.append(result_dict)

                except Exception as e:
                    logger.warning(f"Search strategy {search_query['strategy']} failed: {e}")
                    continue

            # Apply Reciprocal Rank Fusion (RRF) for result combination
            fused_results = self._apply_reciprocal_rank_fusion(all_results)

            log_structured(
                logger,
                "info",
                f"Enhanced multi-vector search completed",
                extra={
                    "work_item_id": work_item.id,
                    "strategies_used": len(search_queries),
                    "total_results": len(all_results),
                    "fused_results": len(fused_results)
                }
            )

            return fused_results[:k]  # Return top k results

        except Exception as e:
            logger.error(f"Enhanced multi-vector search failed: {e}")
            # Fallback to simple search
            try:
                simple_query = work_item.title or "bug"
                results = await self.search_client.hybrid_search(
                    query_text=simple_query,
                    top=k,
                    filters="assigned_to ne null and assigned_to ne ''"
                )
                return [dict(result) if hasattr(result, '__iter__') else {
                    'assigned_to': getattr(result, 'assigned_to', ''),
                    'score': getattr(result, 'score', 0.0)
                } for result in results]
            except:
                return []

    def _extract_technical_keywords(self, work_item: WorkItem) -> List[str]:
        """Extract technical keywords from work item for enhanced search."""
        keywords = []

        # Common technical terms to look for
        tech_patterns = [
            r'\b(API|REST|SOAP|HTTP|HTTPS|JSON|XML)\b',
            r'\b(database|DB|SQL|query|table|index)\b',
            r'\b(authentication|auth|login|password|token)\b',
            r'\b(payment|transaction|billing|invoice)\b',
            r'\b(error|exception|crash|failure|timeout)\b',
            r'\b(performance|slow|latency|memory|CPU)\b',
            r'\b(UI|UX|frontend|backend|service|microservice)\b',
            r'\b(integration|webhook|callback|notification)\b'
        ]

        text_to_search = ""
        if work_item.title:
            text_to_search += work_item.title + " "
        if work_item.description:
            text_to_search += work_item.description[:200] + " "  # Limit length

        if text_to_search:
            import re
            for pattern in tech_patterns:
                matches = re.findall(pattern, text_to_search, re.IGNORECASE)
                keywords.extend([match.lower() for match in matches])

        # Remove duplicates and return
        return list(set(keywords))

    def _apply_reciprocal_rank_fusion(self, results: List[Dict[str, Any]], k: int = 60) -> List[Dict[str, Any]]:
        """
        Apply Reciprocal Rank Fusion (RRF) to combine results from multiple search strategies.
        RRF formula: score = sum(1 / (k + rank)) for each strategy
        """
        try:
            # Group results by work item ID
            item_groups = {}
            for result in results:
                item_id = result.get('work_item_id', result.get('id', ''))
                if item_id not in item_groups:
                    item_groups[item_id] = {
                        'result': result,
                        'strategy_ranks': {},
                        'rrf_score': 0.0
                    }

                strategy = result.get('search_strategy', 'default')
                weight = result.get('strategy_weight', 1.0)

                # Find rank within this strategy
                strategy_results = [r for r in results if r.get('search_strategy') == strategy]
                strategy_results.sort(key=lambda x: x.get('score', 0), reverse=True)

                rank = next((i for i, r in enumerate(strategy_results)
                           if r.get('work_item_id') == item_id or r.get('id') == item_id), 0)

                item_groups[item_id]['strategy_ranks'][strategy] = rank

                # Calculate RRF score contribution
                rrf_contribution = weight * (1.0 / (k + rank + 1))
                item_groups[item_id]['rrf_score'] += rrf_contribution

            # Sort by RRF score and return
            fused_results = []
            for item_data in item_groups.values():
                result = item_data['result'].copy()
                result['rrf_score'] = item_data['rrf_score']
                result['strategy_ranks'] = item_data['strategy_ranks']
                fused_results.append(result)

            fused_results.sort(key=lambda x: x['rrf_score'], reverse=True)
            return fused_results

        except Exception as e:
            logger.error(f"RRF fusion failed: {e}")
            # Fallback to simple score-based sorting
            return sorted(results, key=lambda x: x.get('score', 0), reverse=True)

    def _calculate_confidence_score(self, candidate: AssignmentCandidate) -> float:
        """
        Calculate final confidence score for an assignment candidate.
        """
        try:
            # Check if this is a realistic iteration specialist - preserve their confidence
            for reason in candidate.reasoning:
                if "Iteration specialist:" in reason:
                    # For realistic iteration assignees, use their pre-calculated confidence
                    return candidate.confidence_score

            # Base score from voting
            vote_score = min(candidate.vote_count / self.knn_k, 1.0)

            # Ownership bonus
            ownership_score = 1.0 if candidate.ownership_match else 0.0

            # Load penalty (inverted - lower load is better)
            load_score = 1.0 - candidate.load_penalty

            # Iteration history bonus - check if reasoning includes iteration history
            iteration_score = 0.0
            for reason in candidate.reasoning:
                if "Iteration history:" in reason:
                    # Extract the number of items from the reasoning
                    try:
                        import re
                        match = re.search(r'Iteration history: (\d+) items', reason)
                        if match:
                            item_count = int(match.group(1))
                            # Scale iteration score based on historical involvement
                            iteration_score = min(item_count / 5.0, 1.0)  # Max score at 5+ items
                    except:
                        iteration_score = 0.5  # Default bonus for iteration involvement
                    break

            # Enhanced weighted combination including iteration history
            confidence = (
                self.similarity_weight * vote_score +
                self.ownership_weight * ownership_score +
                self.load_balance_weight * load_score +
                0.25 * iteration_score  # 25% weight for iteration history
            )

            return min(confidence, 1.0)

        except Exception as e:
            logger.error(f"Error calculating confidence score: {e}")
            return 0.0
    
    def _select_best_candidate(self, candidates: List[AssignmentCandidate]) -> AssignmentCandidate:
        """
        Select the best assignment candidate from the list.
        """
        if not candidates:
            raise ValueError("No candidates provided")
        
        # Sort by confidence score (descending)
        candidates.sort(key=lambda x: x.confidence_score, reverse=True)
        
        return candidates[0]

    def _extract_iteration_name(self, iteration_path: str) -> str:
        """
        Extract meaningful iteration name from iteration path.

        Examples:
        - "Air4 Channels Testing\\Year - 2025\\Environment Issues_ TVTH - 2025" -> "Environment Issues_ TVTH - 2025"
        - "Year - 2025\\Environment Issues_ TVTH - 2025\\Jan TVTH issues" -> "Environment Issues_ TVTH - 2025"
        - "Project\\Sprint 1" -> "Sprint 1"

        Args:
            iteration_path: Full iteration path

        Returns:
            Extracted iteration name for searching
        """
        if not iteration_path:
            log_structured(
                logger,
                "debug",
                "Empty iteration path provided",
                extra={"iteration_path": iteration_path}
            )
            return ""

        # Split the path into parts
        path_parts = iteration_path.split('\\')

        # Look for the main iteration part (usually contains underscore and year patterns)
        # Priority: Find parts that look like "Environment Issues_ TVTH - 2025"
        for i, part in enumerate(path_parts):
            # Check if this part looks like a main iteration (has underscore and year-like pattern)
            if '_' in part and any(char.isdigit() for char in part):
                log_structured(
                    logger,
                    "debug",
                    f"Found main iteration part with underscore and digits",
                    extra={
                        "iteration_path": iteration_path,
                        "found_part": part,
                        "part_index": i,
                        "pattern": "main_iteration"
                    }
                )
                return part.strip()

        # Fallback: Look for parts that contain year patterns (2024, 2025, etc.)
        for i, part in enumerate(path_parts):
            import re
            if re.search(r'\b20\d{2}\b', part):  # Matches 2000-2099
                log_structured(
                    logger,
                    "debug",
                    f"Found iteration part with year pattern",
                    extra={
                        "iteration_path": iteration_path,
                        "found_part": part,
                        "part_index": i,
                        "pattern": "year_pattern"
                    }
                )
                return part.strip()

        # Fallback: Use the last meaningful part (skip very generic parts)
        last_part = path_parts[-1] if path_parts else iteration_path

        # Skip if last part is too generic or short
        generic_parts = ['issues', 'items', 'tasks', 'work', 'bugs']
        if len(last_part) < 3 or last_part.lower() in generic_parts:
            # Use second to last part if available
            if len(path_parts) > 1:
                last_part = path_parts[-2]

        log_structured(
            logger,
            "debug",
            f"Using fallback iteration name extraction",
            extra={
                "iteration_path": iteration_path,
                "path_parts": path_parts,
                "extracted_name": last_part.strip(),
                "pattern": "fallback"
            }
        )
        return last_part.strip()

    def _extract_iteration_hierarchy_patterns(self, iteration_path: str) -> List[str]:
        """
        Extract all possible iteration patterns for comprehensive hierarchy search.

        For "Air4 Channels Testing\\Year - 2025\\Environment Issues_ TVTH - 2025":
        Returns:
        - "Environment Issues_ TVTH - 2025" (main iteration)
        - "Environment Issues_ TVTH - 2025/" (with trailing slash for sub-iterations)
        - "Year - 2025\\Environment Issues_ TVTH - 2025" (parent + main)
        - Full path and all combinations

        Args:
            iteration_path: Full iteration path

        Returns:
            List of search patterns to try
        """
        if not iteration_path:
            return []

        patterns = []
        path_parts = iteration_path.split('\\')

        # Add the full path
        patterns.append(iteration_path)

        # Add the main iteration name
        main_iteration = self._extract_iteration_name(iteration_path)
        if main_iteration:
            patterns.append(main_iteration)
            # Add pattern with trailing slash for sub-iterations
            patterns.append(f"{main_iteration}/")

        # Add combinations of parent paths
        for i in range(len(path_parts)):
            if i > 0:  # Skip the root
                partial_path = '\\'.join(path_parts[:i+1])
                patterns.append(partial_path)

                # Also add pattern for finding sub-iterations
                patterns.append(f"{partial_path}\\")

        # Add patterns for each meaningful part
        for part in path_parts:
            if len(part) > 3 and part not in ['issues', 'items', 'tasks', 'work', 'bugs']:
                patterns.append(part)

        # Remove duplicates while preserving order
        unique_patterns = []
        seen = set()
        for pattern in patterns:
            if pattern not in seen:
                unique_patterns.append(pattern)
                seen.add(pattern)

        log_structured(
            logger,
            "debug",
            f"Generated iteration hierarchy patterns",
            extra={
                "iteration_path": iteration_path,
                "patterns_count": len(unique_patterns),
                "patterns": unique_patterns[:10]  # Log first 10 patterns
            }
        )

        return unique_patterns
