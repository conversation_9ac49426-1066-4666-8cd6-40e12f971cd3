"""
Suggest assignees based on historical assignments for work items
filtered by Iteration Path (including children) and optional comment text
using Azure AI Search for retrieval and Azure DevOps revisions for history.

Usage examples:

  python scripts/suggest_assignees_from_history.py \
    --iteration-path "Air4 Channels Testing\\Year - 2025\\Environment Issues_ TVTH - 2025" \
    --under \
    --comment "TVTH" \
    --top 500

  python scripts/suggest_assignees_from_history.py \
    --iteration-path "Air4 Channels Testing\\Year - 2025\\Environment Issues_ TVTH - 2025\\Jan TVTH issues" \
    --comment "timeout" \
    --top 300

Environment variables (loaded via Config):
  ADO_ORGANIZATION, ADO_PROJECT, ADO_PAT_TOKEN
  AZURE_SEARCH_SERVICE_NAME, AZURE_SEARCH_ADMIN_KEY, SEARCH_INDEX_NAME
"""

import argparse
import asyncio
import collections
from typing import Dict, List, Tuple

from functions.__app__.common.utils.config import Config
from functions.__app__.common.adapters.search_client import SearchClient
from functions.__app__.common.adapters.ado_client import AdoClient


def _normalize_assignee(value) -> str:
    """Normalize AssignedTo field across possible formats into a stable label."""
    if not value:
        return "Unassigned"
    if isinstance(value, str):
        # ADO often returns "Display Name <email>" or just display name
        return value.strip()
    if isinstance(value, dict):
        # Identity reference object
        email = value.get("uniqueName") or value.get("mail") or value.get("principalName")
        display = value.get("displayName") or value.get("name") or value.get("uniqueName")
        return (email or display or "Unassigned").strip()
    return str(value)


async def suggest_assignees(iteration_path: str, include_children: bool, comment: str, top: int) -> List[Tuple[str, int]]:
    config = Config()

    # Initialize clients
    search_client = SearchClient(config)
    ado_client = AdoClient(config)

    try:
        # Query text: use comment if provided; fall back to a generic token
        query_text = comment or "work item"

        # Pull a reasonably large set of candidates from Azure Search
        results = await search_client.hybrid_search(
            query_text=query_text,
            filters=None,
            top=top,
            include_total_count=False,
        )

        # Filter by iteration path
        base = iteration_path.lower().rstrip("\\/")
        filtered = []
        for r in results:
            ipath = (r.iteration_path or "").lower().rstrip("\\/")
            if not ipath:
                continue
            if include_children:
                if ipath.startswith(base):
                    filtered.append(r)
            else:
                if ipath == base:
                    filtered.append(r)

        # Fetch revisions for each work item and count assignees over time
        assignee_counter: Dict[str, int] = collections.Counter()

        for r in filtered:
            wid = r.work_item_id
            revisions = await ado_client.get_revisions(wid)
            last_assignee = None
            for rev in revisions:
                fields = rev.get("fields", {})
                assigned_to = _normalize_assignee(fields.get("System.AssignedTo"))
                # Count only changes to avoid overcounting repeated values
                if assigned_to != last_assignee and assigned_to and assigned_to != "Unassigned":
                    assignee_counter[assigned_to] += 1
                    last_assignee = assigned_to

        # Return top suggestions
        suggestions = assignee_counter.most_common(10)
        return suggestions

    finally:
        await ado_client.close()


def main():
    parser = argparse.ArgumentParser(description="Suggest assignees from historical assignments")
    parser.add_argument("--iteration-path", required=True, help="Base Iteration Path to filter on")
    parser.add_argument("--under", action="store_true", help="Include all child iteration paths (UNDER)")
    parser.add_argument("--comment", default="", help="Optional comment text to search for")
    parser.add_argument("--top", type=int, default=500, help="Max items to pull from Azure Search before filtering")
    args = parser.parse_args()

    suggestions = asyncio.run(suggest_assignees(args.iteration_path, args.under, args.comment, args.top))

    if not suggestions:
        print("No historical assignees found for the given criteria.")
        return

    print("Suggested assignees (by historical assignment frequency):")
    for name, count in suggestions:
        print(f"- {name}: {count}")


if __name__ == "__main__":
    main()

