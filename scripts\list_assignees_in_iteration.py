"""
List assignee suggestions from Azure Search for a given Iteration Path.

This script queries the configured Azure Search index, filters results by
the provided iteration path (optionally including children), and aggregates
the Assigned To field to produce a ranked list of assignees based purely on
indexed search data (no Azure DevOps API calls).

Usage:
  python scripts/list_assignees_in_iteration.py \
    --iteration-path "Air4 Channels Testing\\Year - 2025\\Environment Issues_ TVTH - 2025" \
    --under \
    --top 1000
"""

import argparse
import os
from collections import Counter
import asyncio
from typing import List

from functions.__app__.common.utils.config import Config
from functions.__app__.common.adapters.search_client import SearchClient


def _under_match(candidate: str, base: str) -> bool:
    """Return True if candidate equals base or is under base (prefix path)."""
    if not candidate or not base:
        return False
    c = candidate.rstrip('\\/').lower()
    b = base.rstrip('\\/').lower()
    return c == b or c.startswith(b + '\\')


async def list_assignees(iteration_path: str, include_children: bool, top: int, service_name: str = None, admin_key: str = None, index_name: str = None):
    # Allow overriding config via CLI args without code changes
    if service_name:
        os.environ['AZURE_SEARCH_SERVICE_NAME'] = service_name
    if admin_key:
        os.environ['AZURE_SEARCH_ADMIN_KEY'] = admin_key
    if index_name:
        os.environ['SEARCH_INDEX_NAME'] = index_name

    config = Config()
    search = SearchClient(config)

    # Server-side OData filter for iteration path
    base = iteration_path.rstrip('\\/')
    esc = base.replace("'", "''")
    if include_children:
        # Emulate prefix match via lexicographic range: [base, base + U+FFFF)
        odata_filter = f"(iteration_path ge '{esc}' and iteration_path lt '{esc}\uffff')"
    else:
        odata_filter = f"iteration_path eq '{esc}'"

    results = await search.hybrid_search(
        query_text="*",
        query_vector=None,
        filters=odata_filter,
        top=top,
        include_total_count=False,
    )

    counter = Counter()
    total_considered = 0

    for r in results:
        ipath = (r.iteration_path or '').rstrip('\\/')
        if not ipath:
            continue
        total_considered += 1
        assignee = (r.assigned_to or '').strip()
        if assignee and assignee.lower() not in ('unassigned', 'none'):
            counter[assignee] += 1

    print(f"Iteration base: {iteration_path}")
    print(f"Include children: {include_children}")
    print(f"Results considered: {total_considered}")
    if not counter:
        print("No assignees found in Azure Search for this scope.")
        return

    print("Assignee suggestions (from Azure Search, frequency):")
    for name, cnt in counter.most_common():
        print(f"- {name}: {cnt}")


def main():
    parser = argparse.ArgumentParser(description="List assignees from Azure Search for an iteration path")
    parser.add_argument("--iteration-path", required=True, help="Iteration Path (full path)")
    parser.add_argument("--under", action="store_true", help="Include child iterations (UNDER)")
    parser.add_argument("--top", type=int, default=1000, help="Max docs to retrieve from Azure Search")
    parser.add_argument("--service-name", help="Azure Search service name (optional override)")
    parser.add_argument("--admin-key", help="Azure Search admin/query key (optional override)")
    parser.add_argument("--index", help="Azure Search index name (optional override)")
    args = parser.parse_args()

    asyncio.run(list_assignees(
        iteration_path=args.iteration_path,
        include_children=args.under,
        top=args.top,
        service_name=args.service_name,
        admin_key=args.admin_key,
        index_name=args.index
    ))


if __name__ == "__main__":
    main()
