"""
Stakeholder Routing System for Intelligent Notifications.
Determines the right recipients for notifications based on work item properties and team configuration.
"""

import logging
from typing import List, Dict, Any, Optional, Set
from datetime import datetime

from ..models import (
    NotificationTrigger,
    StakeholderRoute,
    RecipientType,
    DeliveryMethod,
    EscalationLevel,
    ProjectNotificationConfig
)
from ..models.schemas import WorkItem, TeamMember
from ..adapters.ado_client import AdoClient
from ..ownership.codeowners import CodeOwnersParser
from ..ownership.heuristics import OwnershipHeuristics
from ..utils.config import Config
from ..utils.logging import log_structured

logger = logging.getLogger(__name__)


class StakeholderRouter:
    """
    Intelligent stakeholder routing system.
    
    Determines the right recipients for notifications based on:
    - Work item assignee and ownership
    - Code ownership patterns
    - Team configuration
    - Escalation rules
    - Load balancing
    """
    
    def __init__(self, config: Config, ado_client: AdoClient):
        self.config = config
        self.ado_client = ado_client
        self.codeowners_parser = CodeOwnersParser(config)
        self.ownership_heuristics = OwnershipHeuristics(config)
        self._project_configs: Dict[str, ProjectNotificationConfig] = {}
        self._team_members_cache: Dict[str, List[TeamMember]] = {}
        
    def load_project_config(self, project: str, config: ProjectNotificationConfig) -> None:
        """Load project-specific notification configuration."""
        self._project_configs[project] = config
        log_structured(
            logger,
            "info",
            f"Loaded routing config for project: {project}",
            extra={"project": project}
        )
    
    async def route_notification(
        self,
        work_item: WorkItem,
        trigger: NotificationTrigger
    ) -> List[StakeholderRoute]:
        """
        Route a notification to appropriate stakeholders.
        
        Args:
            work_item: Work item to route notification for
            trigger: Notification trigger
        
        Returns:
            List of stakeholder routes
        """
        routes = []
        
        try:
            project_config = self._get_project_config(work_item.project)
            
            # Get routing strategies based on trigger type and escalation level
            strategies = self._get_routing_strategies(trigger, project_config)
            
            for strategy in strategies:
                strategy_routes = await self._execute_routing_strategy(
                    work_item, trigger, strategy, project_config
                )
                routes.extend(strategy_routes)
            
            # Remove duplicates and sort by priority
            routes = self._deduplicate_and_prioritize_routes(routes)
            
            log_structured(
                logger,
                "info",
                f"Routing completed for work item {work_item.id}",
                extra={
                    "work_item_id": work_item.id,
                    "trigger_type": trigger.trigger_type,
                    "routes_count": len(routes),
                    "escalation_level": trigger.escalation_level
                }
            )
            
            return routes
            
        except Exception as e:
            log_structured(
                logger,
                "error",
                f"Error routing notification: {e}",
                extra={"work_item_id": work_item.id, "trigger_type": trigger.trigger_type},
                exc_info=True
            )
            return []
    
    def _get_routing_strategies(
        self,
        trigger: NotificationTrigger,
        config: ProjectNotificationConfig
    ) -> List[str]:
        """Get routing strategies based on trigger and escalation level."""
        strategies = []
        
        # Base routing strategies
        if trigger.escalation_level == EscalationLevel.NORMAL:
            strategies = ["assignee", "team_channel"]
        elif trigger.escalation_level == EscalationLevel.ELEVATED:
            strategies = ["assignee", "code_owners", "team_channel"]
        elif trigger.escalation_level == EscalationLevel.URGENT:
            strategies = ["assignee", "code_owners", "team_channel", "managers"]
        elif trigger.escalation_level == EscalationLevel.CRITICAL:
            strategies = ["assignee", "code_owners", "team_channel", "managers", "pagerduty"]
        
        # Add security-specific routing
        if trigger.trigger_type.value == "security_alert":
            strategies.append("security_team")
        
        return strategies
    
    async def _execute_routing_strategy(
        self,
        work_item: WorkItem,
        trigger: NotificationTrigger,
        strategy: str,
        config: ProjectNotificationConfig
    ) -> List[StakeholderRoute]:
        """Execute a specific routing strategy."""
        
        if strategy == "assignee":
            return await self._route_to_assignee(work_item, trigger)
        elif strategy == "code_owners":
            return await self._route_to_code_owners(work_item, trigger)
        elif strategy == "team_channel":
            return self._route_to_team_channel(work_item, trigger, config)
        elif strategy == "managers":
            return self._route_to_managers(work_item, trigger, config)
        elif strategy == "pagerduty":
            return self._route_to_pagerduty(work_item, trigger, config)
        elif strategy == "security_team":
            return await self._route_to_security_team(work_item, trigger)
        else:
            return []
    
    async def _route_to_assignee(
        self,
        work_item: WorkItem,
        trigger: NotificationTrigger
    ) -> List[StakeholderRoute]:
        """Route notification to work item assignee."""
        routes = []
        
        if work_item.assigned_to:
            # Get assignee details
            assignee_details = await self._get_user_details(work_item.assigned_to)
            
            if assignee_details:
                route = StakeholderRoute(
                    recipient_type=RecipientType.USER,
                    recipient_id=work_item.assigned_to,
                    recipient_name=assignee_details.get("displayName", work_item.assigned_to),
                    priority=1,  # Highest priority
                    escalation_level=trigger.escalation_level,
                    delivery_method=DeliveryMethod.TEAMS_ADAPTIVE_CARD,
                    routing_reason="Direct assignee",
                    confidence_score=1.0
                )
                routes.append(route)
        
        return routes
    
    async def _route_to_code_owners(
        self,
        work_item: WorkItem,
        trigger: NotificationTrigger
    ) -> List[StakeholderRoute]:
        """Route notification to code owners based on affected areas."""
        routes = []
        
        try:
            # Extract file paths from work item (this would need to be enhanced
            # to actually parse file paths from description, tags, or related commits)
            file_paths = self._extract_file_paths(work_item)
            
            if file_paths:
                owners = await self.codeowners_parser.get_owners_for_paths(file_paths)
                
                for owner in owners:
                    route = StakeholderRoute(
                        recipient_type=RecipientType.USER,
                        recipient_id=owner,
                        recipient_name=owner,
                        priority=2,
                        escalation_level=trigger.escalation_level,
                        delivery_method=DeliveryMethod.TEAMS_ADAPTIVE_CARD,
                        routing_reason="Code owner",
                        confidence_score=0.8
                    )
                    routes.append(route)
        
        except Exception as e:
            log_structured(
                logger,
                "warning",
                f"Error routing to code owners: {e}",
                extra={"work_item_id": work_item.id}
            )
        
        return routes
    
    def _route_to_team_channel(
        self,
        work_item: WorkItem,
        trigger: NotificationTrigger,
        config: ProjectNotificationConfig
    ) -> List[StakeholderRoute]:
        """Route notification to team channel."""
        routes = []
        
        if config.default_team_channel:
            route = StakeholderRoute(
                recipient_type=RecipientType.TEAM_CHANNEL,
                recipient_id=config.default_team_channel,
                recipient_name=f"{work_item.project} Team Channel",
                priority=3,
                escalation_level=trigger.escalation_level,
                delivery_method=DeliveryMethod.TEAMS_ADAPTIVE_CARD,
                routing_reason="Team channel notification",
                confidence_score=0.9
            )
            routes.append(route)
        
        return routes
    
    def _route_to_managers(
        self,
        work_item: WorkItem,
        trigger: NotificationTrigger,
        config: ProjectNotificationConfig
    ) -> List[StakeholderRoute]:
        """Route notification to escalation managers."""
        routes = []
        
        for manager_email in config.escalation_managers:
            route = StakeholderRoute(
                recipient_type=RecipientType.USER,
                recipient_id=manager_email,
                recipient_name=manager_email,
                priority=4,
                escalation_level=trigger.escalation_level,
                delivery_method=DeliveryMethod.EMAIL_HTML,
                routing_reason="Escalation manager",
                confidence_score=0.7
            )
            routes.append(route)
        
        return routes
    
    def _route_to_pagerduty(
        self,
        work_item: WorkItem,
        trigger: NotificationTrigger,
        config: ProjectNotificationConfig
    ) -> List[StakeholderRoute]:
        """Route notification to PagerDuty for critical escalation."""
        routes = []
        
        if config.pagerduty_service_key and trigger.escalation_level == EscalationLevel.CRITICAL:
            route = StakeholderRoute(
                recipient_type=RecipientType.PAGERDUTY,
                recipient_id=config.pagerduty_service_key,
                recipient_name="PagerDuty Incident",
                priority=5,
                escalation_level=trigger.escalation_level,
                delivery_method=DeliveryMethod.PAGERDUTY_INCIDENT,
                routing_reason="Critical escalation",
                confidence_score=1.0
            )
            routes.append(route)
        
        return routes
    
    async def _route_to_security_team(
        self,
        work_item: WorkItem,
        trigger: NotificationTrigger
    ) -> List[StakeholderRoute]:
        """Route security alerts to security team."""
        routes = []
        
        # This would typically query for security team members
        # Get security team email from configuration
        security_team_email = getattr(self.config, 'SECURITY_TEAM_EMAIL', '<EMAIL>')
        
        route = StakeholderRoute(
            recipient_type=RecipientType.USER,
            recipient_id=security_team_email,
            recipient_name="Security Team",
            priority=1,  # High priority for security
            escalation_level=trigger.escalation_level,
            delivery_method=DeliveryMethod.EMAIL_HTML,
            routing_reason="Security alert",
            confidence_score=1.0
        )
        routes.append(route)
        
        return routes
    
    def _deduplicate_and_prioritize_routes(
        self,
        routes: List[StakeholderRoute]
    ) -> List[StakeholderRoute]:
        """Remove duplicate routes and sort by priority."""
        
        # Deduplicate by recipient_id and delivery_method
        seen = set()
        deduplicated = []
        
        for route in routes:
            key = (route.recipient_id, route.delivery_method)
            if key not in seen:
                seen.add(key)
                deduplicated.append(route)
        
        # Sort by priority (lower number = higher priority)
        deduplicated.sort(key=lambda r: (r.priority, -r.confidence_score))
        
        return deduplicated
    
    def _extract_file_paths(self, work_item: WorkItem) -> List[str]:
        """Extract file paths from work item content."""
        # This is a simplified implementation
        # In practice, you'd parse file paths from:
        # - Work item description
        # - Tags
        # - Related commits
        # - Attached files
        
        file_paths = []
        
        # Look for common file path patterns in description
        if work_item.description:
            import re
            # Simple regex for file paths
            path_pattern = r'[a-zA-Z0-9_/\\.-]+\.[a-zA-Z]{2,4}'
            matches = re.findall(path_pattern, work_item.description)
            file_paths.extend(matches)
        
        return file_paths
    
    async def _get_user_details(self, user_id: str) -> Optional[Dict[str, Any]]:
        """Get user details from Azure DevOps."""
        try:
            # This would call ADO API to get user details
            # For now, return a placeholder
            return {
                "displayName": user_id,
                "emailAddress": user_id
            }
        except Exception as e:
            log_structured(
                logger,
                "warning",
                f"Error getting user details for {user_id}: {e}"
            )
            return None
    
    def _get_project_config(self, project: str) -> ProjectNotificationConfig:
        """Get project configuration or return default."""
        if project in self._project_configs:
            return self._project_configs[project]
        
        # Return default configuration
        return ProjectNotificationConfig(project_name=project)
