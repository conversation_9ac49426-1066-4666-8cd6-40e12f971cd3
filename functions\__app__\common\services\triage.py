"""
Work Item Triage Service
Provides assignment suggestions and priority recommendations based on REAL historical data from ADO.
NO HALLUCINATION - Only uses actual assignees from historical work items and comments.
"""

import logging
import re
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime, timedelta
from collections import defaultdict, Counter

from ..vectorstore import SearchHit
from ..adapters.ado_client import AdoClient
from ..utils.config import get_config

logger = logging.getLogger(__name__)


class TriageService:
    """
    Service for triaging work items with assignment and priority suggestions.
    Uses ONLY real historical data from ADO - no hallucination allowed.
    """

    def __init__(self, config=None):
        self.config = config or get_config()
        self.ado_client = None  # Will be initialized when needed
    
    async def suggest_assignees(
        self,
        work_item_id: int,
        title: str,
        description: str,
        work_item_type: str,
        similar_items: List[Dict[str, Any]],
        k: int = 3
    ) -> List[Dict[str, Any]]:
        """
        Suggest top assignees based on REAL historical data from ADO.
        NO HALLUCINATION - Only uses actual assignees from historical work items and comments.

        Args:
            work_item_id: Current work item ID
            title: Work item title
            description: Work item description
            work_item_type: Type of work item (Bug, Task, etc.)
            similar_items: List of similar historical work items from vector search
            k: Number of assignee suggestions to return

        Returns:
            List of assignee suggestions with scores and rationale based on REAL data
        """
        try:
            if not similar_items:
                logger.warning(f"No similar items found for work item {work_item_id}")
                # Fallback: derive suggestions from recent items in same iteration/area
                fallback = await self._fallback_assignees_from_wiql(work_item_id)
                if fallback:
                    return fallback[:k]
                return []

            # Initialize ADO client if needed
            if not self.ado_client:
                from __app__.common.adapters.ado_client import AdoClient
                self.ado_client = AdoClient(self.config)

            # Extract real assignees from similar items
            assignee_votes = Counter()
            assignee_details = defaultdict(lambda: {
                'historical_count': 0,
                'similarity_scores': [],
                'work_item_types': [],
                'iteration_paths': [],
                'area_paths': [],
                'work_item_ids': []
            })

            # Process similar items to extract REAL assignees
            for item in similar_items:
                item_id = item.get('id')
                assignee = item.get('assigned_to', '').strip()
                similarity_score = item.get('score', 0.0)

                # Skip if no assignee or unassigned
                if not assignee or assignee.lower() in ['unassigned', '', 'none']:
                    continue

                # Count votes weighted by similarity
                weight = max(0.1, similarity_score)  # Minimum weight of 0.1
                assignee_votes[assignee] += weight

                # Store details for rationale
                assignee_details[assignee]['historical_count'] += 1
                assignee_details[assignee]['similarity_scores'].append(similarity_score)
                assignee_details[assignee]['work_item_types'].append(item.get('work_item_type', ''))
                assignee_details[assignee]['work_item_ids'].append(item_id)

            # Get assignees from comments of similar work items
            comment_assignees = await self._extract_assignees_from_comments(
                [item.get('id') for item in similar_items if item.get('id')]
            )

            # Add comment-based assignees with lower weight
            for work_item_id_ref, mentioned_assignees in comment_assignees.items():
                for assignee in mentioned_assignees:
                    if assignee and assignee.strip():
                        assignee_votes[assignee] += 0.5  # Lower weight for comment mentions
                        assignee_details[assignee]['historical_count'] += 1

            if not assignee_votes:
                logger.warning(f"No real assignees found in historical data for work item {work_item_id}")
                # Fallback: try WIQL-based history within same iteration/area
                fallback = await self._fallback_assignees_from_wiql(work_item_id)
                if fallback:
                    return fallback[:k]
                return []

            # Get current workloads for real assignees only
            real_assignees = list(assignee_votes.keys())
            workload_data = await self._get_real_current_workloads(real_assignees)

            # Calculate final scores with workload penalty
            final_suggestions = []
            for assignee, vote_score in assignee_votes.most_common(k * 2):  # Get more candidates
                details = assignee_details[assignee]
                current_workload = workload_data.get(assignee, 0)

                # Apply workload penalty
                workload_penalty = min(0.5, current_workload * 0.05)  # Max 50% penalty
                final_score = max(0.1, vote_score - workload_penalty)

                # Calculate confidence based on historical data
                avg_similarity = sum(details['similarity_scores']) / len(details['similarity_scores']) if details['similarity_scores'] else 0
                confidence = min(1.0, final_score * avg_similarity)

                # Generate rationale based on REAL data
                rationale = self._generate_real_assignee_rationale(assignee, details, current_workload)

                suggestion = {
                    "name": assignee,
                    "email": self._extract_email_from_assignee(assignee),
                    "score": confidence,
                    "rationale": rationale,
                    "historical_count": details['historical_count'],
                    "current_workload": current_workload,
                    "avg_similarity": avg_similarity,
                    "data_source": "real_ado_history"
                }
                final_suggestions.append(suggestion)

            # Sort by confidence and return top k
            final_suggestions.sort(key=lambda x: x['score'], reverse=True)
            final_suggestions = final_suggestions[:k]

            logger.info(
                f"Generated {len(final_suggestions)} REAL assignee suggestions for work item {work_item_id}",
                extra={
                    "work_item_id": work_item_id,
                    "suggestions_count": len(final_suggestions),
                    "top_assignee": final_suggestions[0]['name'] if final_suggestions else None,
                    "data_source": "real_ado_history"
                }
            )

            return final_suggestions

        except Exception as e:
            logger.error(f"Error suggesting assignees for work item {work_item_id}: {e}")
            return []
    
    async def suggest_priority(
        self,
        work_item: Dict[str, Any],
        similar_items: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        Suggest priority based on historical patterns and content analysis.
        
        Args:
            work_item: Current work item
            similar_items: List of similar historical work items
            
        Returns:
            Priority suggestion with confidence and rationale
        """
        try:
            # Extract work item details
            title = work_item.get('fields', {}).get('System.Title', '').lower()
            description = work_item.get('fields', {}).get('System.Description', '').lower()
            work_item_type = work_item.get('fields', {}).get('System.WorkItemType', '')
            
            # Analyze historical priorities
            historical_priorities = self._analyze_historical_priorities(similar_items)
            
            # Apply priority rules
            rule_based_priority = self._apply_priority_rules(title, description, work_item_type)
            
            # Combine historical and rule-based suggestions
            suggested_priority = self._combine_priority_suggestions(
                historical_priorities, 
                rule_based_priority
            )
            
            return suggested_priority
            
        except Exception as e:
            logger.error(f"Error suggesting priority: {e}")
            return {
                "priority": 3,  # Default medium priority
                "confidence": 0.0,
                "rationale": f"Error in priority analysis: {e}",
                "historical_data": {},
                "rule_based": {}
            }
    
    async def _extract_assignees_from_comments(self, work_item_ids: List[int]) -> Dict[int, List[str]]:
        """
        Extract assignee mentions from work item comments using REAL ADO data.
        NO HALLUCINATION - Only extracts actual email addresses and names from comments.
        """
        assignee_mentions = {}

        if not work_item_ids:
            return assignee_mentions

        # Email pattern for extracting emails from comments
        email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'

        # Assignee mention patterns
        assignee_patterns = [
            r'assign(?:ed)?\s+to\s+([A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,})',
            r'@([A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,})',
            r'assignee:\s*([A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,})',
        ]

        for work_item_id in work_item_ids:
            try:
                # Get real comments from ADO
                comments = await self.ado_client.get_comments(work_item_id)
                assignees = set()

                for comment in comments:
                    comment_text = comment.get('text', '').lower()

                    # Extract emails using patterns
                    for pattern in assignee_patterns:
                        matches = re.findall(pattern, comment_text, re.IGNORECASE)
                        assignees.update(matches)

                    # Also extract all emails mentioned in comments
                    email_matches = re.findall(email_pattern, comment_text, re.IGNORECASE)
                    assignees.update(email_matches)

                if assignees:
                    assignee_mentions[work_item_id] = list(assignees)

            except Exception as e:
                logger.warning(f"Error extracting assignees from comments for work item {work_item_id}: {e}")
                continue

        logger.info(f"Extracted assignees from {len(assignee_mentions)} work items with comments")
        return assignee_mentions

    async def _get_real_current_workloads(self, assignees: List[str]) -> Dict[str, int]:
        """
        Get REAL current workload for assignees from ADO.
        NO HALLUCINATION - Queries actual active work items.
        """
        workloads = {}

        if not assignees:
            return workloads

        try:
            for assignee in assignees:
                # Query for REAL active work items assigned to this person
                try:
                    # Use WIQL to get active items
                    wiql_query = f"""
                    SELECT [System.Id], [System.Title], [System.State]
                    FROM WorkItems
                    WHERE [System.AssignedTo] = '{assignee}'
                    AND [System.State] NOT IN ('Closed', 'Resolved', 'Done', 'Removed')
                    """

                    active_items = await self.ado_client.query_work_items(wiql_query)
                    workloads[assignee] = len(active_items) if active_items else 0

                except Exception as e:
                    logger.warning(f"Error getting workload for {assignee}: {e}")
                    workloads[assignee] = 0  # Default to 0 if can't query

        except Exception as e:
            logger.error(f"Error getting real workloads: {e}")
            # Default to 0 for all assignees if there's an error
            workloads = {assignee: 0 for assignee in assignees}

        logger.info(f"Retrieved real workloads for {len(workloads)} assignees")
        return workloads

    async def _fallback_assignees_from_wiql(self, work_item_id: int) -> List[Dict[str, Any]]:
        """Fallback: use WIQL to find recent related items and suggest assignees.
        Uses same iteration path (UNDER) or area path when available.
        Returns list of {email, name, score, rationale} sorted by frequency.
        """
        try:
            # Ensure ADO client
            if not self.ado_client:
                from __app__.common.adapters.ado_client import AdoClient
                self.ado_client = AdoClient(self.config)

            wi = await self.ado_client.get_work_item(work_item_id)
            if not wi:
                return []

            fields = wi.get('fields', {})
            iteration_path = fields.get('System.IterationPath')
            area_path = fields.get('System.AreaPath')
            work_item_type = fields.get('System.WorkItemType') or 'Bug'
            project = fields.get('System.TeamProject') or getattr(self.config, 'ADO_PROJECT', '')

            # Build WIQL targeting same iteration (UNDER) else same area
            scope_clause = None
            if iteration_path:
                scope_clause = f"[System.IterationPath] UNDER '{iteration_path.replace("'", "''")}'"
            elif area_path:
                scope_clause = f"[System.AreaPath] UNDER '{area_path.replace("'", "''")}'"
            else:
                scope_clause = f"[System.TeamProject] = '{project.replace("'", "''")}'"

            wiql_query = f"""
                SELECT [System.Id], [System.Title], [System.AssignedTo], [System.State], [System.ChangedDate]
                FROM WorkItems
                WHERE {scope_clause}
                AND [System.WorkItemType] = '{work_item_type.replace("'", "''")}'
                AND [System.ChangedDate] >= @Today - 180
                ORDER BY [System.ChangedDate] DESC
            """

            items = await self.ado_client.query_work_items(wiql_query)
            if not items:
                return []

            from collections import Counter
            counts = Counter()
            for it in items:
                f = it.get('fields', {})
                assignee = f.get('System.AssignedTo')
                if isinstance(assignee, dict):
                    name = assignee.get('displayName') or assignee.get('uniqueName') or ''
                    email = assignee.get('uniqueName') or assignee.get('mail') or name
                else:
                    # Might be "Display Name <email>" or just a name
                    name = str(assignee) if assignee else ''
                    email = name
                if email and name and name.lower() not in ['unassigned', '']:
                    counts[(name, email)] += 1

            if not counts:
                return []

            suggestions = []
            total = sum(counts.values()) or 1
            for (name, email), cnt in counts.most_common(10):
                score = min(1.0, max(0.1, cnt / total))
                suggestions.append({
                    'email': email,
                    'name': name,
                    'score': score,
                    'rationale': f"Historical frequency in {('iteration ' + iteration_path) if iteration_path else ('area ' + area_path if area_path else project)}: {cnt}"
                })

            return suggestions

        except Exception as e:
            logger.warning(f"Fallback WIQL assignee suggestion failed for {work_item_id}: {e}")
            return []

    def _extract_email_from_assignee(self, assignee: str) -> str:
        """Extract email from assignee string if it contains one."""
        email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
        matches = re.findall(email_pattern, assignee)
        return matches[0] if matches else assignee

    def _generate_real_assignee_rationale(
        self,
        assignee: str,
        details: Dict[str, Any],
        current_workload: int
    ) -> str:
        """Generate rationale based on REAL historical data."""
        rationale_parts = []

        historical_count = details.get('historical_count', 0)
        if historical_count > 0:
            rationale_parts.append(f"handled {historical_count} similar items")

        avg_similarity = sum(details['similarity_scores']) / len(details['similarity_scores']) if details['similarity_scores'] else 0
        if avg_similarity > 0.7:
            rationale_parts.append(f"high similarity match ({avg_similarity:.2f})")

        if current_workload == 0:
            rationale_parts.append("no current active items")
        elif current_workload < 5:
            rationale_parts.append(f"low workload ({current_workload} items)")
        elif current_workload > 10:
            rationale_parts.append(f"high workload ({current_workload} items)")

        work_item_types = details.get('work_item_types', [])
        if work_item_types:
            type_counts = Counter(work_item_types)
            most_common_type = type_counts.most_common(1)[0][0] if type_counts else ''
            if most_common_type:
                rationale_parts.append(f"experience with {most_common_type}s")

        if not rationale_parts:
            rationale_parts.append("found in historical data")

        return f"{assignee}: " + ", ".join(rationale_parts)
    
    async def _get_current_workloads(
        self,
        assignees: List[str],
        project: str
    ) -> Dict[str, int]:
        """Get current workload for assignees."""
        workloads = {}
        
        try:
            # Query for active work items assigned to each person
            for assignee in assignees:
                # Simplified workload calculation
                # TODO: Implement proper WIQL query for active items
                workloads[assignee] = 5  # Placeholder - assume 5 active items
                
        except Exception as e:
            logger.warning(f"Error getting workloads: {e}")
            # Default to moderate workload
            workloads = {assignee: 5 for assignee in assignees}
        
        return workloads
    
    def _apply_workload_penalty(
        self,
        assignee_scores: Dict[str, Dict[str, Any]],
        workload_data: Dict[str, int]
    ) -> Dict[str, Dict[str, Any]]:
        """Apply workload penalty to assignee scores."""
        # Workload penalty parameters
        max_workload = 15  # Items above this get heavy penalty
        penalty_factor = 0.1  # Penalty per item above optimal
        
        for assignee, score_data in assignee_scores.items():
            current_workload = workload_data.get(assignee, 5)
            score_data['current_workload'] = current_workload
            
            # Calculate penalty
            if current_workload > max_workload:
                penalty = (current_workload - max_workload) * penalty_factor
            else:
                penalty = 0
            
            # Apply penalty to base score
            final_score = max(0, score_data['base_score'] - penalty)
            score_data['final_score'] = final_score
            score_data['workload_penalty'] = penalty
        
        return assignee_scores
    
    async def _get_ownership_data(self, area_path: str, project: str) -> List[str]:
        """Get ownership data for area path (simplified)."""
        # TODO: Implement CODEOWNERS or area path ownership lookup
        # For now, return empty list
        return []
    
    def _generate_assignee_rationale(
        self,
        assignee: str,
        score_data: Dict[str, Any]
    ) -> str:
        """Generate human-readable rationale for assignee suggestion."""
        rationale_parts = []
        
        historical_count = score_data.get('historical_count', 0)
        if historical_count > 0:
            rationale_parts.append(f"handled {historical_count} similar items")
        
        if score_data.get('ownership_match', False):
            rationale_parts.append("area ownership match")
        
        workload = score_data.get('current_workload', 0)
        if workload < 5:
            rationale_parts.append("low current workload")
        elif workload > 10:
            rationale_parts.append("high current workload")
        
        if not rationale_parts:
            rationale_parts.append("general assignment candidate")
        
        return f"{assignee}: " + ", ".join(rationale_parts)
    
    def _analyze_historical_priorities(
        self,
        similar_items: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Analyze priority patterns from similar items."""
        if not similar_items:
            return {"priorities": {}, "most_common": 3, "confidence": 0.0}

        priority_counts = defaultdict(int)
        weighted_sum = 0
        total_weight = 0

        for item in similar_items:
            # Handle both SearchHit objects and dict format
            if hasattr(item, 'metadata'):
                priority = item.metadata.get('priority', 3)  # SearchHit format
                weight = item.score
            else:
                priority = item.get('priority', 3)  # Dict format
                weight = item.get('score', 0.5)

            priority_counts[priority] += 1
            weighted_sum += priority * weight
            total_weight += weight
        
        # Most common priority
        most_common_priority = max(priority_counts.items(), key=lambda x: x[1])[0]
        
        # Weighted average priority
        avg_priority = weighted_sum / total_weight if total_weight > 0 else 3
        
        # Confidence based on consistency
        total_items = len(similar_items)
        most_common_count = priority_counts[most_common_priority]
        confidence = most_common_count / total_items if total_items > 0 else 0.0
        
        return {
            "priorities": dict(priority_counts),
            "most_common": most_common_priority,
            "weighted_average": avg_priority,
            "confidence": confidence,
            "total_items": total_items
        }
    
    def _apply_priority_rules(
        self,
        title: str,
        description: str,
        work_item_type: str
    ) -> Dict[str, Any]:
        """Apply rule-based priority classification."""
        text = f"{title} {description}".lower()
        
        # High priority keywords
        high_priority_keywords = [
            'critical', 'urgent', 'production', 'outage', 'down', 'broken',
            'security', 'vulnerability', 'data loss', 'corruption'
        ]
        
        # Low priority keywords
        low_priority_keywords = [
            'enhancement', 'feature request', 'nice to have', 'cosmetic',
            'documentation', 'cleanup', 'refactor'
        ]
        
        # Check for high priority indicators
        high_priority_matches = [kw for kw in high_priority_keywords if kw in text]
        low_priority_matches = [kw for kw in low_priority_keywords if kw in text]
        
        if high_priority_matches:
            suggested_priority = 1  # High
            confidence = 0.8
            rationale = f"High priority keywords: {', '.join(high_priority_matches)}"
        elif low_priority_matches:
            suggested_priority = 4  # Low
            confidence = 0.6
            rationale = f"Low priority keywords: {', '.join(low_priority_matches)}"
        elif work_item_type.lower() == 'bug':
            suggested_priority = 2  # Medium-High for bugs
            confidence = 0.5
            rationale = "Bug work item type"
        else:
            suggested_priority = 3  # Medium default
            confidence = 0.3
            rationale = "Default medium priority"
        
        return {
            "priority": suggested_priority,
            "confidence": confidence,
            "rationale": rationale,
            "high_priority_keywords": high_priority_matches,
            "low_priority_keywords": low_priority_matches
        }
    
    def _combine_priority_suggestions(
        self,
        historical_data: Dict[str, Any],
        rule_based: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Combine historical and rule-based priority suggestions."""
        historical_priority = historical_data.get('most_common', 3)
        historical_confidence = historical_data.get('confidence', 0.0)
        
        rule_priority = rule_based.get('priority', 3)
        rule_confidence = rule_based.get('confidence', 0.0)
        
        # Weight the suggestions based on confidence
        if historical_confidence > 0.7 and rule_confidence > 0.7:
            # Both confident - average them
            final_priority = round((historical_priority + rule_priority) / 2)
            final_confidence = (historical_confidence + rule_confidence) / 2
            rationale = f"Historical pattern ({historical_priority}) + Rule-based ({rule_priority})"
        elif historical_confidence > rule_confidence:
            # Trust historical data more
            final_priority = historical_priority
            final_confidence = historical_confidence
            rationale = f"Based on {historical_data.get('total_items', 0)} similar items"
        else:
            # Trust rule-based more
            final_priority = rule_priority
            final_confidence = rule_confidence
            rationale = rule_based.get('rationale', 'Rule-based classification')
        
        return {
            "priority": final_priority,
            "confidence": final_confidence,
            "rationale": rationale,
            "historical_data": historical_data,
            "rule_based": rule_based
        }
    
    async def get_triage_recommendations(
        self,
        work_item_id: int,
        title: str,
        description: str,
        work_item_type: str,
        similar_items: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        Get comprehensive triage recommendations for a work item using REAL historical data.
        NO HALLUCINATION - Only uses actual assignees and data from ADO.

        Args:
            work_item_id: Current work item ID
            title: Work item title
            description: Work item description
            work_item_type: Type of work item (Bug, Task, etc.)
            similar_items: List of similar historical work items from vector search

        Returns:
            Complete triage recommendations including assignees and priority based on REAL data
        """
        try:
            # Get REAL assignee suggestions from historical data
            suggested_assignees = await self.suggest_assignees(
                work_item_id=work_item_id,
                title=title,
                description=description,
                work_item_type=work_item_type,
                similar_items=similar_items
            )

            # Get priority suggestion (this method already uses real data)
            work_item_dict = {
                'id': work_item_id,
                'fields': {
                    'System.Title': title,
                    'System.Description': description,
                    'System.WorkItemType': work_item_type
                }
            }
            priority_recommendation = await self.suggest_priority(work_item_dict, similar_items)

            # Determine if auto-assignment should be applied
            auto_assign_confidence = getattr(self.config, 'AUTO_ASSIGN_MIN_CONF', 0.8)
            should_auto_assign = (
                suggested_assignees and
                suggested_assignees[0]['score'] >= auto_assign_confidence and
                not getattr(self.config, 'READ_ONLY', True)
            )

            return {
                "suggested_assignees": suggested_assignees,
                "priority_recommendation": priority_recommendation,
                "auto_assign_recommended": should_auto_assign,
                "auto_assign_threshold": auto_assign_confidence,
                "similar_items_count": len(similar_items),
                "data_source": "real_ado_history",
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"Error getting triage recommendations for work item {work_item_id}: {e}")
            return {
                "suggested_assignees": [],
                "priority_recommendation": {
                    "priority": 3,
                    "confidence": 0.0,
                    "rationale": f"Error in triage analysis: {e}"
                },
                "auto_assign_recommended": False,
                "error": str(e),
                "data_source": "error"
            }
