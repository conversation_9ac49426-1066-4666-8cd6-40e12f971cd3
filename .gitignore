# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
#  Usually these files are written by a python script from a template
#  before PyInstaller builds the exe, so as to inject date/other infos into it.
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# celery beat schedule file
celerybeat-schedule

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Azure Functions artifacts
bin
obj
appsettings.json
local.settings.json

# Azure
.azure/

# VS Code
.vscode/

# JetBrains
.idea/

# macOS
.DS_Store

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini

# Temporary files
*.tmp
*.temp
*.log
*.html

# Configuration files with secrets
*.config.json
email_config.json
team_config.json

# Test outputs
test_results/
test_output/

# Development artifacts
data/
samples/
ml/
scripts/test_*.py
scripts/check_*.py
scripts/analyze_*.py
scripts/find_*.py
scripts/get_*.py
scripts/send_*.py
scripts/simple_*.py
scripts/debug_*.py
scripts/diagnose_*.py

# Summary and temporary documentation
*_SUMMARY.md
*_FIX_SUMMARY.md
POST_REFACTOR_*.md
CLEANUP_*.md
REFACTOR_*.md
